package com.fasnote.alm.plugin.manage.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Optional;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.fasnote.alm.plugin.manage.model.PluginLicense;
import com.fasnote.alm.plugin.manage.model.ValidationResult;
import com.fasnote.alm.plugin.manage.test.LicenseTestBase;

/**
 * 许可证服务测试类
 */
@DisplayName("许可证服务测试")
class LicenseServiceTest extends LicenseTestBase {
    
    @AfterEach
    void tearDown() {
        cleanupTest();
    }
    
    @Test
    @DisplayName("验证有效许可证应该成功")
    void shouldValidateValidLicense() {
        // Given
        PluginLicense license = createTestLicense();
        when(mockSecurityValidator.validateLicense(any(PluginLicense.class)))
            .thenReturn(ValidationResult.success("验证成功"));
        
        // When
        ValidationResult result = licenseService.validateLicense(license);
        
        // Then
        assertTrue(result.isValid());
        assertEquals("验证成功", result.getMessage());
        verify(mockSecurityValidator).validateLicense(license);
    }
    
    @Test
    @DisplayName("验证空许可证应该失败")
    void shouldFailToValidateNullLicense() {
        // When
        ValidationResult result = licenseService.validateLicense(null);
        
        // Then
        assertFalse(result.isValid());
        assertEquals("许可证为空", result.getMessage());
        verify(mockSecurityValidator, never()).validateLicense(any());
    }
    
    @Test
    @DisplayName("获取存在的许可证应该成功")
    void shouldGetExistingLicense() {
        // Given
        PluginLicense license = createTestLicense();
        licenseRepository.save(license);
        
        // When
        Optional<PluginLicense> result = licenseService.getLicense(TEST_PLUGIN_ID);
        
        // Then
        assertTrue(result.isPresent());
        assertEquals(TEST_PLUGIN_ID, result.get().getPluginId());
    }
    
    @Test
    @DisplayName("获取不存在的许可证应该返回空")
    void shouldReturnEmptyForNonExistentLicense() {
        // When
        Optional<PluginLicense> result = licenseService.getLicense("non-existent-plugin");
        
        // Then
        assertFalse(result.isPresent());
    }
    
    @Test
    @DisplayName("检查功能可用性应该正确")
    void shouldCheckFeatureAvailability() {
        // Given
        PluginLicense license = createTestLicense();
        licenseRepository.save(license);
        when(mockSecurityValidator.validateLicense(any(PluginLicense.class)))
            .thenReturn(ValidationResult.success("验证成功"));
        
        // When & Then
        assertTrue(licenseService.isFeatureEnabled("BasicFeature"));
        assertTrue(licenseService.isFeatureEnabled("AdvancedFeature"));
        assertFalse(licenseService.isFeatureEnabled("PremiumFeature")); // 在测试数据中为false
        assertFalse(licenseService.isFeatureEnabled("NonExistentFeature"));
        
        // 测试空功能名称的情况
        assertFalse(licenseService.isFeatureEnabled(null));
        assertFalse(licenseService.isFeatureEnabled(""));
        assertFalse(licenseService.isFeatureEnabled("   "));
    }
}