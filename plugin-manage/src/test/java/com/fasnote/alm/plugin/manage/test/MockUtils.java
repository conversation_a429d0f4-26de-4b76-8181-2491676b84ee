package com.fasnote.alm.plugin.manage.test;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Properties;

import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.osgi.framework.Bundle;
import org.osgi.framework.BundleContext;
import org.osgi.framework.wiring.BundleWiring;

import com.fasnote.alm.plugin.manage.audit.AuditLogger;
import com.fasnote.alm.plugin.manage.config.LicenseConfiguration;
import com.fasnote.alm.plugin.manage.core.LicenseManager;
import com.fasnote.alm.plugin.manage.model.PluginLicense;
import com.fasnote.alm.plugin.manage.model.ValidationResult;
import com.fasnote.alm.plugin.manage.security.SecurityValidator;
import com.fasnote.alm.license.crypto.RSALicenseEncryption;

/**
 * Mock工具类
 * 提供创建和配置各种Mock对象的便捷方法
 */
public class MockUtils {
    
    /**
     * 创建Mock的BundleContext
     */
    public static BundleContext createMockBundleContext() {
        BundleContext mockContext = mock(BundleContext.class);
        Bundle mockBundle = createMockBundle("com.test.plugin", Bundle.ACTIVE);
        
        when(mockContext.getBundles()).thenReturn(new Bundle[]{mockBundle});
        when(mockContext.getBundle()).thenReturn(mockBundle);
        
        return mockContext;
    }
    
    /**
     * 创建Mock的Bundle
     */
    public static Bundle createMockBundle(String symbolicName, int state) {
        Bundle mockBundle = mock(Bundle.class);
        BundleWiring mockWiring = mock(BundleWiring.class);
        
        when(mockBundle.getSymbolicName()).thenReturn(symbolicName);
        when(mockBundle.getState()).thenReturn(state);
        when(mockBundle.adapt(BundleWiring.class)).thenReturn(mockWiring);
        when(mockWiring.getClassLoader()).thenReturn(MockUtils.class.getClassLoader());
        
        return mockBundle;
    }
    
    /**
     * 创建Mock的LicenseConfiguration（开发环境）
     */
    public static LicenseConfiguration createMockLicenseConfigurationForDev(File tempDir) {
        LicenseConfiguration mockConfig = mock(LicenseConfiguration.class);
        
        when(mockConfig.getLicenseDirectory()).thenReturn(tempDir.getAbsolutePath());
        when(mockConfig.isEncryptionEnabled()).thenReturn(false); // 开发环境关闭加密
        when(mockConfig.isMachineBindingEnabled()).thenReturn(false); // 开发环境关闭机器绑定
        when(mockConfig.isSignatureValidationEnabled()).thenReturn(false); // 开发环境关闭签名验证
        when(mockConfig.isAuditEnabled()).thenReturn(true);
        when(mockConfig.getCacheSize()).thenReturn(50);
        when(mockConfig.getCacheTTL()).thenReturn(1800L);
        when(mockConfig.getLicenseCheckInterval()).thenReturn(600L);
        when(mockConfig.getConfigValue("license.development.mode", "false")).thenReturn("true");
        
        return mockConfig;
    }
    
    /**
     * 创建Mock的LicenseConfiguration（生产环境）
     */
    public static LicenseConfiguration createMockLicenseConfigurationForProd(File tempDir) {
        LicenseConfiguration mockConfig = mock(LicenseConfiguration.class);
        
        when(mockConfig.getLicenseDirectory()).thenReturn(tempDir.getAbsolutePath());
        when(mockConfig.isEncryptionEnabled()).thenReturn(true); // 生产环境启用加密
        when(mockConfig.isMachineBindingEnabled()).thenReturn(true); // 生产环境启用机器绑定
        when(mockConfig.isSignatureValidationEnabled()).thenReturn(true); // 生产环境启用签名验证
        when(mockConfig.isAuditEnabled()).thenReturn(true);
        when(mockConfig.getCacheSize()).thenReturn(100);
        when(mockConfig.getCacheTTL()).thenReturn(3600L);
        when(mockConfig.getLicenseCheckInterval()).thenReturn(300L);
        when(mockConfig.getConfigValue("license.development.mode", "false")).thenReturn("false");
        
        return mockConfig;
    }
    
    /**
     * 创建Mock的RSALicenseEncryption
     */
    public static RSALicenseEncryption createMockRSALicenseEncryption() throws Exception {
        RSALicenseEncryption mockEncryption = mock(RSALicenseEncryption.class);

        // 模拟RSA解密验证：返回测试数据
        when(mockEncryption.decryptAndVerifyLicensePackage(any())).thenAnswer(new Answer<Map<String, byte[]>>() {
            @Override
            public Map<String, byte[]> answer(InvocationOnMock invocation) throws Throwable {
                // 在测试环境中，我们简单地返回一些测试数据
                Map<String, byte[]> testClasses = new HashMap<>();
                testClasses.put("com.test.TestClass", "test-class-data".getBytes("UTF-8"));
                return testClasses;
            }
        });

        return mockEncryption;
    }
    
    /**
     * 创建Mock的SecurityValidator
     */
    public static SecurityValidator createMockSecurityValidator(boolean validationResult) {
        SecurityValidator mockValidator = mock(SecurityValidator.class);
        
        ValidationResult result = validationResult ? 
            ValidationResult.success("验证成功") : 
            ValidationResult.failure("验证失败");
            
        when(mockValidator.validateLicense(any(PluginLicense.class))).thenReturn(result);
        when(mockValidator.validateSignature(any(PluginLicense.class))).thenReturn(result);
        when(mockValidator.validateMachineBinding(any(PluginLicense.class))).thenReturn(result);
        when(mockValidator.validateTimeLimit(any(PluginLicense.class))).thenReturn(result);
        
        return mockValidator;
    }
    
    /**
     * 创建Mock的AuditLogger（静默模式）
     */
    public static AuditLogger createSilentMockAuditLogger() {
        AuditLogger mockLogger = mock(AuditLogger.class);
        
        doNothing().when(mockLogger).info(anyString());
        doNothing().when(mockLogger).warn(anyString());
        doNothing().when(mockLogger).error(anyString());
        doNothing().when(mockLogger).debug(anyString());
        // 移除不存在的重载方法
        
        return mockLogger;
    }
    
    /**
     * 创建测试用的RSA密钥对
     */
    public static KeyPair generateTestKeyPair() throws Exception {
        KeyPairGenerator keyGen = KeyPairGenerator.getInstance("RSA");
        keyGen.initialize(2048);
        return keyGen.generateKeyPair();
    }
    
    /**
     * 创建测试许可证文件
     */
    public static File createTestLicenseFile(File directory, String pluginId, String content) throws IOException {
        File licenseFile = new File(directory, pluginId + ".lic");
        try (FileOutputStream fos = new FileOutputStream(licenseFile)) {
            fos.write(content.getBytes("UTF-8"));
        }
        return licenseFile;
    }
    
    /**
     * 创建测试配置文件
     */
    public static File createTestConfigFile(File directory, Properties properties) throws IOException {
        File configFile = new File(directory, "license-config.properties");
        try (FileOutputStream fos = new FileOutputStream(configFile)) {
            properties.store(fos, "Test Configuration");
        }
        return configFile;
    }
    
    /**
     * 创建包含多个插件的Mock LicenseManager
     */
    public static LicenseManager createMockLicenseManagerWithPlugins(String... pluginIds) {
        LicenseManager mockManager = mock(LicenseManager.class);
        Map<String, PluginLicense> licenses = new HashMap<>();
        
        for (String pluginId : pluginIds) {
            PluginLicense license = createTestLicense(pluginId, false);
            licenses.put(pluginId, license);
            
            when(mockManager.getPluginLicense(pluginId)).thenReturn(Optional.of(license));
            when(mockManager.hasValidLicense(pluginId)).thenReturn(true);
        }
        
        // 移除不存在的方法调用
        
        return mockManager;
    }
    
    /**
     * 创建测试用的PluginLicense
     */
    public static PluginLicense createTestLicense(String pluginId, boolean expired) {
        // 创建许可证JSON数据
        LocalDateTime currentTime = LocalDateTime.now();
        LocalDateTime expiryTime = expired ? currentTime.minusDays(1) : currentTime.plusYears(1);

        String licenseJson = String.format(
            "{\n" +
            "  \"pluginId\": \"%s\",\n" +
            "  \"productName\": \"Test Product\",\n" +
            "  \"licenseType\": \"COMMERCIAL\",\n" +
            "  \"expiryDate\": \"%s\",\n" +
            "  \"maxUsers\": 100,\n" +
            "  \"machineCode\": \"TEST_MACHINE_CODE\"\n" +
            "}",
            pluginId, expiryTime.format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
        );

        // 使用正确的构造函数
        PluginLicense license = new PluginLicense(pluginId, licenseJson);

        // 设置过期时间
        license.setExpiryDate(expiryTime);

        // 设置机器码
        license.setMachineCode("TEST_MACHINE_CODE");

        return license;
    }
    
    /**
     * 验证Mock对象的特定方法调用
     */
    public static void verifyMethodCalled(Object mock, String methodName, int times) {
        try {
            verify(mock, times(times)).getClass().getMethod(methodName).invoke(mock);
        } catch (Exception e) {
            // 这是一个简化的验证方法，实际使用时应该使用Mockito的verify方法
        }
    }
    
    /**
     * 创建测试环境的系统属性
     */
    public static void setupTestSystemProperties() {
        System.setProperty("license.test.mode", "true");
        System.setProperty("license.development.mode", "true");
        System.setProperty("license.skip.machine.binding", "true");
        System.setProperty("license.skip.signature.validation", "true");
    }
    
    /**
     * 清理测试环境的系统属性
     */
    public static void cleanupTestSystemProperties() {
        System.clearProperty("license.test.mode");
        System.clearProperty("license.development.mode");
        System.clearProperty("license.skip.machine.binding");
        System.clearProperty("license.skip.signature.validation");
    }
}
