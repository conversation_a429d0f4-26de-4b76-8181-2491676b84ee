package com.fasnote.alm.plugin.manage.exception;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * 许可证异常测试类
 */
@DisplayName("许可证异常测试")
class LicenseExceptionTest {
    
    @Test
    @DisplayName("创建基本异常应该成功")
    void shouldCreateBasicException() {
        // Given
        String message = "测试异常消息";
        
        // When
        LicenseException exception = new LicenseException(message);
        
        // Then
        assertEquals(message, exception.getMessage());
        assertNull(exception.getErrorCode());
        assertNull(exception.getParameters());
    }
    
    @Test
    @DisplayName("创建带错误码的异常应该成功")
    void shouldCreateExceptionWithErrorCode() {
        // Given
        String message = "测试异常消息";
        String errorCode = "TEST_ERROR";
        
        // When
        LicenseException exception = new LicenseException(message, errorCode);
        
        // Then
        assertEquals(message, exception.getMessage());
        assertEquals(errorCode, exception.getErrorCode());
    }
    
    @Test
    @DisplayName("创建带参数的异常应该成功")
    void shouldCreateExceptionWithParameters() {
        // Given
        String message = "测试异常消息";
        String errorCode = "TEST_ERROR";
        Object[] parameters = {"param1", "param2", 123};
        
        // When
        LicenseException exception = new LicenseException(message, errorCode, parameters);
        
        // Then
        assertEquals(message, exception.getMessage());
        assertEquals(errorCode, exception.getErrorCode());
        assertArrayEquals(parameters, exception.getParameters());
    }
    
    @Test
    @DisplayName("创建带原因异常应该成功")
    void shouldCreateExceptionWithCause() {
        // Given
        String message = "测试异常消息";
        String errorCode = "TEST_ERROR";
        RuntimeException cause = new RuntimeException("原因异常");
        
        // When
        LicenseException exception = new LicenseException(message, errorCode, cause);
        
        // Then
        assertEquals(message, exception.getMessage());
        assertEquals(errorCode, exception.getErrorCode());
        assertEquals(cause, exception.getCause());
    }
    
    @Test
    @DisplayName("获取格式化消息应该正确")
    void shouldGetFormattedMessage() {
        // Given
        String message = "测试异常消息";
        String errorCode = "TEST_ERROR";
        Object[] parameters = {"param1", 123};
        
        // When
        LicenseException exception = new LicenseException(message, errorCode, parameters);
        String formattedMessage = exception.getFormattedMessage();
        
        // Then
        assertTrue(formattedMessage.contains(message));
        assertTrue(formattedMessage.contains(errorCode));
        assertTrue(formattedMessage.contains("param1"));
        assertTrue(formattedMessage.contains("123"));
    }
    
    @Test
    @DisplayName("toString方法应该返回有用信息")
    void shouldReturnUsefulToString() {
        // Given
        String message = "测试异常消息";
        String errorCode = "TEST_ERROR";
        
        // When
        LicenseException exception = new LicenseException(message, errorCode);
        String toString = exception.toString();
        
        // Then
        assertTrue(toString.contains("LicenseException"));
        assertTrue(toString.contains(message));
        assertTrue(toString.contains(errorCode));
    }
    
    @Test
    @DisplayName("许可证验证异常应该正确创建")
    void shouldCreateLicenseValidationException() {
        // Given
        String pluginId = "test-plugin";
        String message = "许可证验证失败";
        
        // When
        LicenseValidationException exception = new LicenseValidationException(message, pluginId);
        
        // Then
        assertEquals(message, exception.getMessage());
        assertEquals("LICENSE_VALIDATION_FAILED", exception.getErrorCode());
        assertArrayEquals(new Object[]{pluginId}, exception.getParameters());
    }
    
    @Test
    @DisplayName("许可证未找到异常应该正确创建")
    void shouldCreateLicenseNotFoundException() {
        // Given
        String pluginId = "test-plugin";
        
        // When
        LicenseNotFoundException exception = new LicenseNotFoundException(pluginId);
        
        // Then
        assertTrue(exception.getMessage().contains(pluginId));
        assertEquals("LICENSE_NOT_FOUND", exception.getErrorCode());
        assertArrayEquals(new Object[]{pluginId}, exception.getParameters());
    }
    
    @Test
    @DisplayName("许可证注册异常应该正确创建")
    void shouldCreateLicenseRegistrationException() {
        // Given
        String pluginId = "test-plugin";
        String message = "许可证注册失败";
        
        // When
        LicenseRegistrationException exception = new LicenseRegistrationException(message, pluginId);
        
        // Then
        assertEquals(message, exception.getMessage());
        assertEquals("LICENSE_REGISTRATION_FAILED", exception.getErrorCode());
        assertArrayEquals(new Object[]{pluginId}, exception.getParameters());
    }
}