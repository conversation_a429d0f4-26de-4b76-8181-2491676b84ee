package com.fasnote.alm.plugin.manage.registry;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * LicenseServiceRegistry 单元测试
 */
@RunWith(MockitoJUnitRunner.class)
public class LicenseServiceRegistryTest {

    private LicenseServiceRegistry registry;

    @Before
    public void setUp() {
        registry = new LicenseServiceRegistry();
    }

    @Test
    public void testRegisterAndGetServiceByName() {
        // 测试按名称注册和获取服务
        String serviceName = "testService";
        String serviceInstance = "testInstance";
        
        // 注册服务
        registry.registerService(serviceName, serviceInstance);
        
        // 获取服务
        String retrievedService = registry.getService(serviceName);
        
        assertEquals("应该返回注册的服务实例", serviceInstance, retrievedService);
    }

    @Test
    public void testRegisterAndGetServiceByType() {
        // 测试按类型注册和获取服务
        String serviceInstance = "testInstance";
        
        // 注册服务
        registry.registerService(String.class, serviceInstance);
        
        // 获取服务
        String retrievedService = registry.getService(String.class);
        
        assertEquals("应该返回注册的服务实例", serviceInstance, retrievedService);
    }

    @Test
    public void testRegisterServiceByNameAlsoRegistersByType() {
        // 测试按名称注册服务时也会按类型注册
        String serviceName = "testService";
        StringBuilder serviceInstance = new StringBuilder("test");
        
        // 按名称注册服务
        registry.registerService(serviceName, serviceInstance);
        
        // 应该可以按类型获取
        StringBuilder retrievedByType = registry.getService(StringBuilder.class);
        assertEquals("按名称注册的服务也应该可以按类型获取", serviceInstance, retrievedByType);
        
        // 也应该可以按名称获取
        StringBuilder retrievedByName = registry.getService(serviceName);
        assertEquals("应该可以按名称获取服务", serviceInstance, retrievedByName);
    }

    @Test
    public void testGetNonExistentServiceByName() {
        // 测试获取不存在的服务（按名称）
        String result = registry.getService("nonExistentService");
        assertNull("不存在的服务应该返回null", result);
    }

    @Test
    public void testGetNonExistentServiceByType() {
        // 测试获取不存在的服务（按类型）
        Integer result = registry.getService(Integer.class);
        assertNull("不存在的服务应该返回null", result);
    }

    @Test
    public void testGetNamedService() {
        // 测试获取命名服务
        String serviceName = "namedService";
        String serviceInstance = "namedInstance";
        
        // 注册服务
        registry.registerService(serviceName, serviceInstance);
        
        // 按类型和名称获取
        String retrieved = registry.getService(String.class, serviceName);
        assertEquals("应该返回命名服务", serviceInstance, retrieved);
    }

    @Test
    public void testGetNamedServiceFallbackToDefault() {
        // 测试命名服务回退到默认服务
        String defaultInstance = "defaultInstance";
        
        // 只注册默认服务（按类型）
        registry.registerService(String.class, defaultInstance);
        
        // 尝试获取命名服务，应该回退到默认服务
        String retrieved = registry.getService(String.class, "nonExistentName");
        assertEquals("应该回退到默认服务", defaultInstance, retrieved);
    }

    @Test
    public void testGetNamedServiceWithWrongType() {
        // 测试获取命名服务但类型不匹配
        String serviceName = "testService";
        String serviceInstance = "testInstance";
        
        // 注册String类型的服务
        registry.registerService(serviceName, serviceInstance);
        
        // 尝试以Integer类型获取
        Integer retrieved = registry.getService(Integer.class, serviceName);
        assertNull("类型不匹配的命名服务应该返回null", retrieved);
    }

    @Test
    public void testCreateComponent() {
        // 测试创建组件
        StringBuilder component = registry.createComponent(StringBuilder.class);
        
        assertNotNull("创建的组件不应为null", component);
        assertTrue("应该是StringBuilder类型", component instanceof StringBuilder);
    }

    @Test
    public void testCreateComponentWithNoDefaultConstructor() {
        // 测试创建没有默认构造函数的组件
        Integer component = registry.createComponent(Integer.class);
        
        // Integer没有无参构造函数，应该返回null
        assertNull("没有默认构造函数的类应该返回null", component);
    }

    @Test
    public void testCheckModuleInstallationStatus() {
        // 测试检查模块安装状态
        try {
            registry.checkModuleInstallationStatus();
            // 如果没有抛出异常，说明方法执行成功
            assertTrue("模块状态检查应该成功执行", true);
        } catch (Exception e) {
            fail("模块状态检查不应该抛出异常: " + e.getMessage());
        }
    }

    @Test
    public void testGetDependencyInjector() {
        // 测试获取依赖注入器
        Object injector = registry.getDependencyInjector();
        
        assertNotNull("依赖注入器不应为null", injector);
        // 在简化实现中，返回的是registry自身
        assertEquals("应该返回registry自身", registry, injector);
    }

    @Test
    public void testMultipleServicesOfSameType() {
        // 测试同一类型的多个服务
        String service1 = "service1";
        String service2 = "service2";
        
        // 注册两个String类型的服务
        registry.registerService("service1", service1);
        registry.registerService("service2", service2);
        
        // 按类型获取应该返回最后注册的
        String retrieved = registry.getService(String.class);
        assertEquals("按类型获取应该返回最后注册的服务", service2, retrieved);
        
        // 按名称获取应该返回对应的服务
        String retrieved1 = registry.getService("service1");
        String retrieved2 = registry.getService("service2");
        assertEquals("应该返回对应的命名服务", service1, retrieved1);
        assertEquals("应该返回对应的命名服务", service2, retrieved2);
    }

    @Test
    public void testServiceOverride() {
        // 测试服务覆盖
        String serviceName = "overrideTest";
        String originalService = "original";
        String newService = "new";
        
        // 注册原始服务
        registry.registerService(serviceName, originalService);
        String retrieved1 = registry.getService(serviceName);
        assertEquals("应该返回原始服务", originalService, retrieved1);
        
        // 覆盖服务
        registry.registerService(serviceName, newService);
        String retrieved2 = registry.getService(serviceName);
        assertEquals("应该返回新服务", newService, retrieved2);
    }

    @Test
    public void testNullServiceRegistration() {
        // 测试注册null服务
        try {
            registry.registerService("nullService", null);
            Object retrieved = registry.getService("nullService");
            assertNull("注册的null服务应该返回null", retrieved);
        } catch (Exception e) {
            // 如果抛出异常也是可以接受的
            assertTrue("注册null服务时可能抛出异常", true);
        }
    }

    @Test
    public void testEmptyServiceName() {
        // 测试空服务名称
        String serviceInstance = "testInstance";
        
        registry.registerService("", serviceInstance);
        String retrieved = registry.getService("");
        assertEquals("空名称的服务应该可以正常注册和获取", serviceInstance, retrieved);
    }
}
