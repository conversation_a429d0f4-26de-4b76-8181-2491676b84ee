package com.fasnote.alm.plugin.manage.model;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.fasnote.alm.plugin.manage.test.LicenseTestBase;


/**
 * 插件许可证模型测试类
 */
@DisplayName("插件许可证模型测试")
class PluginLicenseTest extends LicenseTestBase {

    @Test
    @DisplayName("创建有效许可证应该成功")
    void shouldCreateValidLicense() {
        // When
        PluginLicense license = createTestLicense();

        // Then
        assertNotNull(license);
        assertEquals(TEST_PLUGIN_ID, license.getPluginId());
        assertEquals(TEST_PRODUCT_NAME, license.getProductName());
        assertEquals(TEST_VERSION, license.getVersion());
        assertEquals(TEST_LICENSE_TYPE, license.getLicenseType());
        assertEquals(TEST_ISSUER, license.getIssuer());
        assertEquals(TEST_LICENSED_TO, license.getLicensedTo());
        assertEquals(TEST_ORGANIZATION, license.getOrganization());
        assertEquals(TEST_MAX_USERS, license.getMaxUsers());
    }

    @Test
    @DisplayName("有效许可证应该通过有效性检查")
    void shouldPassValidityCheck() {
        // Given
        PluginLicense license = createTestLicense();

        // When & Then
        assertTrue(license.isValid());
        assertTrue(license.isEffective());
        assertFalse(license.isExpired());
    }

    @Test
    @DisplayName("过期许可证应该失败有效性检查")
    void shouldFailValidityCheckForExpiredLicense() {
        // Given
        PluginLicense license = createExpiredTestLicense();

        // When & Then
        assertFalse(license.isValid());
        assertTrue(license.isEffective());
        assertTrue(license.isExpired());
    }

    @Test
    @DisplayName("未生效许可证应该失败有效性检查")
    void shouldFailValidityCheckForFutureLicense() {
        // Given
        PluginLicense license = createFutureTestLicense();

        // When & Then
        assertFalse(license.isValid());
        assertFalse(license.isEffective());
        assertFalse(license.isExpired());
    }

    @Test
    @DisplayName("永久许可证应该永不过期")
    void shouldNeverExpireForPermanentLicense() {
        // Given - 创建永久许可证（无过期时间）
        PluginLicense license = createPermanentTestLicense();

        // When & Then
        assertFalse(license.isExpired());
        assertTrue(license.isEffective()); // 应该已生效
        assertTrue(license.isValid()); // 永久许可证应该有效
        assertNull(license.getExpiryDate()); // 过期时间应该为null
    }

    @Test
    @DisplayName("检查功能支持应该正确")
    void shouldCheckFeatureSupport() {
        // Given
        PluginLicense license = createTestLicense();

        // When & Then
        assertTrue(license.hasFeature("BasicFeature"));
        assertTrue(license.hasFeature("AdvancedFeature"));
        assertFalse(license.hasFeature("PremiumFeature"));
        assertFalse(license.hasFeature("NonExistentFeature"));
    }

    @Test
    @DisplayName("获取功能限制应该正确")
    void shouldGetFeatureLimitations() {
        // Given
        PluginLicense license = createTestLicense();

        // When & Then
        assertEquals(100, license.getFeatureLimit("maxProjects"));
        assertEquals("10GB", license.getFeatureLimit("maxStorage"));
        assertNull(license.getFeatureLimit("nonExistentLimit"));
    }

    @Test
    @DisplayName("获取签名内容应该正确")
    void shouldGetContentForSignature() {
        // Given
        PluginLicense license = createTestLicense();

        // When
        String content = license.getContentForSignature();

        // Then
        assertNotNull(content);
        assertTrue(content.contains("pluginId=" + TEST_PLUGIN_ID));
        assertTrue(content.contains("productName=" + TEST_PRODUCT_NAME));
        assertTrue(content.contains("version=" + TEST_VERSION));
        assertTrue(content.contains("licenseType=" + TEST_LICENSE_TYPE));
        assertTrue(content.contains("maxUsers=" + TEST_MAX_USERS));
        // 签名字段本身不应该包含在内容中
        assertFalse(content.contains("signature="));
    }

    @Test
    @DisplayName("获取哈希内容应该正确")
    void shouldGetContentForHash() {
        // Given
        PluginLicense license = createTestLicense();

        // When
        String hashContent = license.getContentForHash();
        String signatureContent = license.getContentForSignature();

        // Then
        assertEquals(signatureContent, hashContent);
    }

    @Test
    @DisplayName("toString方法应该返回有用信息")
    void shouldReturnUsefulToString() {
        // Given
        PluginLicense license = createTestLicense();

        // When
        String toString = license.toString();

        // Then
        assertNotNull(toString);
        assertTrue(toString.contains(TEST_PLUGIN_ID));
        assertTrue(toString.contains(TEST_PRODUCT_NAME));
        assertTrue(toString.contains(TEST_VERSION));
        assertTrue(toString.contains(TEST_LICENSE_TYPE));
    }

    @Test
    @DisplayName("设置和获取加密类数据应该正确")
    void shouldSetAndGetEncryptedClassData() {
        // Given
        PluginLicense license = createTestLicense();
        byte[] testData = "test encrypted data".getBytes();

        // When
        license.setEncryptedClassData(testData);

        // Then
        assertTrue(license.hasEncryptedClasses());
        assertArrayEquals(testData, license.getEncryptedClassData());
    }

    @Test
    @DisplayName("没有加密类数据时应该返回false")
    void shouldReturnFalseWhenNoEncryptedClasses() {
        // Given
        PluginLicense license = createTestLicense();

        // When & Then
        assertFalse(license.hasEncryptedClasses());
        assertNull(license.getEncryptedClassData());
    }

    @Test
    @DisplayName("设置和获取服务映射应该正确")
    void shouldSetAndGetServiceMappings() {
        // Given
        PluginLicense license = createTestLicense();
        java.util.Map<String, String> mappings = new java.util.HashMap<>();
        mappings.put("ITestService", "TestServiceImpl");
        mappings.put("IAnotherService", "AnotherServiceImpl");

        // When
        license.setServiceMappings(mappings);

        // Then
        assertTrue(license.hasServiceMappings());
        assertEquals(2, license.getServiceMappings().size());
        assertEquals("TestServiceImpl", license.getServiceMappings().get("ITestService"));
        assertEquals("AnotherServiceImpl", license.getServiceMappings().get("IAnotherService"));
    }

    @Test
    @DisplayName("没有服务映射时应该返回false")
    void shouldReturnFalseWhenNoServiceMappings() {
        // Given
        PluginLicense license = createTestLicense();

        // When & Then
        assertFalse(license.hasServiceMappings());
        assertTrue(license.getServiceMappings().isEmpty());
    }
}