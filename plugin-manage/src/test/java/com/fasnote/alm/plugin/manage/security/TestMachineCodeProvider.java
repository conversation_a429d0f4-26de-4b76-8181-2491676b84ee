package com.fasnote.alm.plugin.manage.security;

/**
 * 测试用机器码提供者
 * 返回固定的测试机器码，避免依赖 Polarion 环境
 */
public class TestMachineCodeProvider implements MachineCodeProvider {
    
    private final String testMachineCode;
    
    public TestMachineCodeProvider() {
        this.testMachineCode = "TEST-MACHINE-CODE-12345";
    }
    
    public TestMachineCodeProvider(String testMachineCode) {
        this.testMachineCode = testMachineCode;
    }
    
    @Override
    public String getMachineCode() throws Exception {
        return testMachineCode;
    }
}
