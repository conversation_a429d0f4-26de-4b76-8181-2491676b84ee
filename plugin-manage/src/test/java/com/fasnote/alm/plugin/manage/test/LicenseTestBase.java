package com.fasnote.alm.plugin.manage.test;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.fasnote.alm.plugin.manage.audit.AuditLogger;
import com.fasnote.alm.plugin.manage.model.PluginLicense;
import com.fasnote.alm.plugin.manage.repository.LicenseRepository;
import com.fasnote.alm.plugin.manage.repository.impl.InMemoryLicenseRepository;
import com.fasnote.alm.plugin.manage.security.SecurityValidator;
import com.fasnote.alm.plugin.manage.service.LicenseService;
import com.fasnote.alm.plugin.manage.service.impl.LicenseServiceImpl;
import com.fasnote.alm.plugin.manage.util.JsonUtil;

/**
 * 许可证测试基类
 * 提供通用的测试设置和工具方法
 */
public abstract class LicenseTestBase {
    
    protected static final String TEST_PLUGIN_ID = "test-plugin";
    protected static final String TEST_PRODUCT_NAME = "Test Product";
    protected static final String TEST_VERSION = "1.0.0";
    protected static final String TEST_LICENSE_TYPE = "STANDARD";
    protected static final String TEST_ISSUER = "FasNote";
    protected static final String TEST_LICENSED_TO = "Test User";
    protected static final String TEST_ORGANIZATION = "Test Organization";
    protected static final int TEST_MAX_USERS = 10;
    
    protected LicenseRepository licenseRepository;
    protected LicenseService licenseService;
    
    @Mock
    protected SecurityValidator mockSecurityValidator;
    
    @Mock
    protected AuditLogger mockAuditLogger;
    
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 使用内存存储库进行测试
        licenseRepository = new InMemoryLicenseRepository();
        
        // 创建服务实例
        licenseService = new LicenseServiceImpl(licenseRepository, mockSecurityValidator, mockAuditLogger);
        
        // 执行子类的额外设置
        setupTest();
    }
    
    /**
     * 子类可以重写此方法进行额外的测试设置
     */
    protected void setupTest() {
        // 默认空实现
    }
    
    /**
     * 创建测试用的许可证数据（JSON格式）
     *
     * @return 许可证JSON数据
     */
    protected String createTestLicenseJson() {
        return createTestLicenseJson(TEST_PLUGIN_ID, LocalDateTime.now().plusDays(30));
    }
    
    /**
     * 创建测试用的许可证数据（JSON格式）
     *
     * @param pluginId 插件ID
     * @param expiryDate 过期时间
     * @return 许可证JSON数据
     */
    protected String createTestLicenseJson(String pluginId, LocalDateTime expiryDate) {
        Map<String, Object> licenseData = new HashMap<>();
        
        // 基本信息
        licenseData.put("pluginId", pluginId);
        licenseData.put("productName", TEST_PRODUCT_NAME);
        licenseData.put("version", TEST_VERSION);
        licenseData.put("licenseType", TEST_LICENSE_TYPE);
        licenseData.put("issuer", TEST_ISSUER);
        
        // 时间信息
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        licenseData.put("issueDate", LocalDateTime.now().format(formatter));
        licenseData.put("effectiveDate", LocalDateTime.now().format(formatter));
        if (expiryDate != null) {
            licenseData.put("expiryDate", expiryDate.format(formatter));
        }
        // 如果expiryDate为null，则不添加该字段，表示永久许可证
        
        // 用户信息
        licenseData.put("licensedTo", TEST_LICENSED_TO);
        licenseData.put("organization", TEST_ORGANIZATION);
        licenseData.put("maxUsers", TEST_MAX_USERS);
        
        // 功能信息
        Map<String, Object> features = new HashMap<>();
        features.put("BasicFeature", true);
        features.put("AdvancedFeature", true);
        features.put("PremiumFeature", false);
        licenseData.put("features", features);
        
        // 限制信息
        Map<String, Object> limitations = new HashMap<>();
        limitations.put("maxProjects", 100);
        limitations.put("maxStorage", "10GB");
        licenseData.put("limitations", limitations);
        
        // 安全信息
        licenseData.put("machineCode", "TEST-MACHINE-CODE");
        licenseData.put("signature", "TEST-SIGNATURE");
        licenseData.put("contentHash", "TEST-HASH");
        
        return JsonUtil.toJson(licenseData);
    }
    
    /**
     * 创建测试用的许可证对象
     *
     * @return 许可证对象
     */
    protected PluginLicense createTestLicense() {
        return createTestLicense(TEST_PLUGIN_ID, LocalDateTime.now().plusDays(30));
    }
    
    /**
     * 创建测试用的许可证对象
     *
     * @param pluginId 插件ID
     * @param expiryDate 过期时间
     * @return 许可证对象
     */
    protected PluginLicense createTestLicense(String pluginId, LocalDateTime expiryDate) {
        String licenseJson = createTestLicenseJson(pluginId, expiryDate);
        return new PluginLicense(pluginId, licenseJson);
    }
    
    /**
     * 创建过期的测试许可证
     *
     * @return 过期的许可证对象
     */
    protected PluginLicense createExpiredTestLicense() {
        return createTestLicense(TEST_PLUGIN_ID, LocalDateTime.now().minusDays(1));
    }
    
    /**
     * 创建未生效的测试许可证
     *
     * @return 未生效的许可证对象
     */
    protected PluginLicense createFutureTestLicense() {
        String licenseJson = createTestLicenseJson(TEST_PLUGIN_ID, LocalDateTime.now().plusDays(30));
        
        // 修改生效时间为未来
        Map<String, Object> licenseData = JsonUtil.parseJson(licenseJson);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        licenseData.put("effectiveDate", LocalDateTime.now().plusDays(1).format(formatter));
        
        String modifiedJson = JsonUtil.toJson(licenseData);
        return new PluginLicense(TEST_PLUGIN_ID, modifiedJson);
    }
    
    /**
     * 创建永久测试许可证（无过期时间）
     *
     * @return 永久许可证对象
     */
    protected PluginLicense createPermanentTestLicense() {
        String licenseJson = createTestLicenseJson(TEST_PLUGIN_ID, null);
        return new PluginLicense(TEST_PLUGIN_ID, licenseJson);
    }
    
    /**
     * 创建永久许可证（无过期时间）
     *
     * @param pluginId 插件ID
     * @return 永久许可证对象
     */
    protected PluginLicense createPermanentTestLicense(String pluginId) {
        String licenseJson = createTestLicenseJson(pluginId, null); // 无过期时间
        return new PluginLicense(pluginId, licenseJson);
    }
    
    /**
     * 清理测试数据
     */
    protected void cleanupTest() {
        if (licenseRepository instanceof InMemoryLicenseRepository) {
            ((InMemoryLicenseRepository) licenseRepository).clear();
        }
    }
}