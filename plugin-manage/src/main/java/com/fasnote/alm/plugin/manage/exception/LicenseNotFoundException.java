package com.fasnote.alm.plugin.manage.exception;

/**
 * 许可证未找到异常
 */
public class LicenseNotFoundException extends LicenseException {
    
    private static final long serialVersionUID = 1L;
    
    public LicenseNotFoundException(String pluginId) {
        super("许可证未找到: " + pluginId, "LICENSE_NOT_FOUND", pluginId);
    }
    
    public LicenseNotFoundException(String message, String pluginId) {
        super(message, "LICENSE_NOT_FOUND", pluginId);
    }
}