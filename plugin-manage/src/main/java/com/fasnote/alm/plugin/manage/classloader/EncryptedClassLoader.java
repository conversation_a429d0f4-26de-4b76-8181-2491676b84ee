package com.fasnote.alm.plugin.manage.classloader;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.net.URL;
import java.security.CodeSource;
import java.security.ProtectionDomain;
import java.security.cert.Certificate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.jar.JarEntry;
import java.util.jar.JarInputStream;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.plugin.manage.exception.SecurityException;

/**
 * 加密类加载器
 * 用于加载存储在许可证文件中的加密核心实现类
 * 
 * 特性：
 * 1. 支持从加密的JAR包中加载类
 * 2. 与OSGi环境兼容
 * 3. 支持类缓存和热加载
 * 4. 提供安全的类隔离
 */
public class EncryptedClassLoader extends ClassLoader {

    private static final Logger logger = LoggerFactory.getLogger(EncryptedClassLoader.class);

    // 已加载的类缓存
    private final Map<String, Class<?>> classCache = new ConcurrentHashMap<>();

    // 解密后的类字节码缓存
    private final Map<String, byte[]> classBytesCache = new ConcurrentHashMap<>();

    // OSGi Bundle ClassLoader（父类加载器）
    private final ClassLoader osgiClassLoader;

    /**
     * 构造函数
     *
     * @param osgiClassLoader OSGi Bundle的类加载器
     */
    public EncryptedClassLoader(ClassLoader osgiClassLoader) {
        super(osgiClassLoader);
        this.osgiClassLoader = osgiClassLoader;

        logger.info("创建加密类加载器，父类加载器: {}", osgiClassLoader.getClass().getName());
    }

    /**
     * 构造函数（兼容方法）
     *
     * @param pluginId 插件ID
     */
    public EncryptedClassLoader(String pluginId) {
        super();
        this.osgiClassLoader = this.getClass().getClassLoader();

        logger.info("创建加密类加载器: {}", pluginId);
    }
    


    /**
     * 添加已解密的JAR包
     *
     * @param jarName JAR包名称
     * @param decryptedJarData 已解密的JAR数据
     */
    public void addDecryptedJar(String jarName, byte[] decryptedJarData) {
        if (jarName == null || decryptedJarData == null) {
            throw new IllegalArgumentException("JAR名称和数据不能为空");
        }

        logger.info("添加已解密JAR包: {}, 大小: {} 字节", jarName, decryptedJarData.length);

        // 直接解析JAR包中的类（数据已经解密）
        try {
            preloadClassesFromDecryptedJar(jarName, decryptedJarData);
        } catch (Exception e) {
            logger.error("预加载已解密JAR包失败: {}", jarName, e);
        }
    }




    


    /**
     * 预加载已解密JAR包中的类信息
     *
     * @param jarName JAR包名称
     * @param decryptedJarData 已解密的JAR数据
     */
    private void preloadClassesFromDecryptedJar(String jarName, byte[] decryptedJarData) throws Exception {
        // 解析JAR包（数据已经解密）
        try (JarInputStream jarStream = new JarInputStream(new ByteArrayInputStream(decryptedJarData))) {
            JarEntry entry;
            while ((entry = jarStream.getNextJarEntry()) != null) {
                String entryName = entry.getName();

                // 只处理.class文件
                if (entryName.endsWith(".class") && !entry.isDirectory()) {
                    // 读取类字节码
                    byte[] classBytes = readAllBytes(jarStream);

                    // 转换文件路径为类名
                    String className = entryName.replace('/', '.').replace(".class", "");

                    // 缓存类字节码
                    classBytesCache.put(className, classBytes);

                    logger.debug("预加载类: {} 从已解密JAR: {}", className, jarName);
                }
            }
        }

        logger.info("已解密JAR包预加载完成: {}, 类数量: {}", jarName, classBytesCache.size());
    }

    /**
     * 读取输入流的所有字节
     * 
     * @param inputStream 输入流
     * @return 字节数组
     */
    private byte[] readAllBytes(InputStream inputStream) throws Exception {
        java.io.ByteArrayOutputStream buffer = new java.io.ByteArrayOutputStream();
        byte[] data = new byte[1024];
        int bytesRead;
        
        while ((bytesRead = inputStream.read(data, 0, data.length)) != -1) {
            buffer.write(data, 0, bytesRead);
        }
        
        return buffer.toByteArray();
    }
    
    @Override
    protected Class<?> findClass(String name) throws ClassNotFoundException {
        logger.debug("查找类: {}", name);
        
        // 检查缓存
        Class<?> cachedClass = classCache.get(name);
        if (cachedClass != null) {
            logger.debug("从缓存加载类: {}", name);
            return cachedClass;
        }
        
        // 从加密的类字节码中加载
        byte[] classBytes = classBytesCache.get(name);
        if (classBytes != null) {
            try {
                // 定义类
                Class<?> clazz = defineClass(name, classBytes, 0, classBytes.length, createProtectionDomain());
                
                // 缓存类
                classCache.put(name, clazz);
                
                logger.info("成功加载加密类: {}", name);
                return clazz;
                
            } catch (Exception e) {
                logger.error("加载加密类失败: {}", name, e);
                throw new ClassNotFoundException("无法加载加密类: " + name, e);
            }
        }
        
        // 如果在加密类中找不到，委托给父类加载器
        logger.debug("委托给父类加载器: {}", name);
        throw new ClassNotFoundException("类未找到: " + name);
    }
    
    @Override
    protected Class<?> loadClass(String name, boolean resolve) throws ClassNotFoundException {
        logger.debug("加载类: {}, resolve: {}", name, resolve);
        
        // 检查是否已经加载
        Class<?> loadedClass = findLoadedClass(name);
        if (loadedClass != null) {
            logger.debug("类已加载: {}", name);
            if (resolve) {
                resolveClass(loadedClass);
            }
            return loadedClass;
        }
        
        // 对于系统类，委托给父类加载器
        if (isSystemClass(name)) {
            logger.debug("系统类，委托给父类加载器: {}", name);
            return super.loadClass(name, resolve);
        }
        
        // 尝试从加密类中加载
        if (classBytesCache.containsKey(name)) {
            try {
                Class<?> clazz = findClass(name);
                if (resolve) {
                    resolveClass(clazz);
                }
                return clazz;
            } catch (ClassNotFoundException e) {
                logger.debug("从加密类加载失败，尝试父类加载器: {}", name);
            }
        }
        
        // 委托给父类加载器（OSGi Bundle ClassLoader）
        return super.loadClass(name, resolve);
    }
    
    /**
     * 检查是否为系统类
     * 
     * @param className 类名
     * @return 是否为系统类
     */
    private boolean isSystemClass(String className) {
        return className.startsWith("java.") ||
               className.startsWith("javax.") ||
               className.startsWith("sun.") ||
               className.startsWith("com.sun.") ||
               className.startsWith("org.osgi.") ||
               className.startsWith("org.eclipse.") ||
               className.startsWith("com.polarion.");
    }
    
    /**
     * 创建保护域
     * 
     * @return 保护域
     */
    private ProtectionDomain createProtectionDomain() {
        try {
            CodeSource codeSource = new CodeSource(new URL("file:encrypted-license"), (Certificate[]) null);
            return new ProtectionDomain(codeSource, null, this, null);
        } catch (Exception e) {
            logger.warn("创建保护域失败，使用默认保护域", e);
            return null;
        }
    }
    
    /**
     * 创建类实例
     * 
     * @param className 类名
     * @return 类实例
     */
    public Object createInstance(String className) throws Exception {
        logger.info("创建类实例: {}", className);
        
        try {
            Class<?> clazz = loadClass(className);
            Object instance = clazz.getDeclaredConstructor().newInstance();
            
            logger.info("成功创建类实例: {}", className);
            return instance;
            
        } catch (Exception e) {
            logger.error("创建类实例失败: {}", className, e);
            throw new SecurityException("无法创建加密类实例: " + className, e);
        }
    }
    
    /**
     * 创建类实例（带参数构造函数）
     * 
     * @param className 类名
     * @param parameterTypes 参数类型
     * @param parameters 参数值
     * @return 类实例
     */
    public Object createInstance(String className, Class<?>[] parameterTypes, Object[] parameters) throws Exception {
        logger.info("创建类实例（带参数）: {}", className);
        
        try {
            Class<?> clazz = loadClass(className);
            Object instance = clazz.getDeclaredConstructor(parameterTypes).newInstance(parameters);
            
            logger.info("成功创建类实例（带参数）: {}", className);
            return instance;
            
        } catch (Exception e) {
            logger.error("创建类实例失败（带参数）: {}", className, e);
            throw new SecurityException("无法创建加密类实例: " + className, e);
        }
    }
    
    /**
     * 检查类是否可用
     * 
     * @param className 类名
     * @return 是否可用
     */
    public boolean isClassAvailable(String className) {
        try {
            loadClass(className);
            return true;
        } catch (ClassNotFoundException e) {
            return false;
        }
    }
    
    /**
     * 获取已加载的类数量
     * 
     * @return 类数量
     */
    public int getLoadedClassCount() {
        return classCache.size();
    }
    
    /**
     * 获取可用的类数量
     *
     * @return 类数量
     */
    public int getAvailableClassCount() {
        return classBytesCache.size();
    }

    /**
     * 获取所有可用的类名
     *
     * @return 类名列表
     */
    public List<String> getAvailableClassNames() {
        return new ArrayList<>(classBytesCache.keySet());
    }

    /**
     * 检查指定类是否可用
     *
     * @param className 类名
     * @return 是否可用
     */
    public boolean hasClass(String className) {
        return classBytesCache.containsKey(className);
    }

    /**
     * 获取类的字节码大小
     *
     * @param className 类名
     * @return 字节码大小，如果类不存在返回-1
     */
    public int getClassSize(String className) {
        byte[] classBytes = classBytesCache.get(className);
        return classBytes != null ? classBytes.length : -1;
    }
    
    /**
     * 清理缓存
     */
    public void clearCache() {
        logger.info("清理类加载器缓存");
        classCache.clear();
        classBytesCache.clear();
    }
    
    /**
     * 获取统计信息
     * 
     * @return 统计信息
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new ConcurrentHashMap<>();
        stats.put("loadedClassCount", classCache.size());
        stats.put("availableClassCount", classBytesCache.size());
        stats.put("parentClassLoader", osgiClassLoader.getClass().getName());
        
        return stats;
    }
    
    @Override
    public String toString() {
        return String.format("EncryptedClassLoader{loadedClasses=%d, availableClasses=%d}",
                           classCache.size(), classBytesCache.size());
    }
}
