package com.fasnote.alm.plugin.manage.web.dto;

/**
 * 插件信息DTO
 */
public class PluginInfoDto {
    private String pluginId;
    private String pluginName;
    private String version;
    private String status;
    private boolean licenseValid;
    private String licenseStatus;
    private String description;

    public PluginInfoDto() {}

    public PluginInfoDto(String pluginId, String pluginName, String version, 
                        String status, boolean licenseValid, String licenseStatus, String description) {
        this.pluginId = pluginId;
        this.pluginName = pluginName;
        this.version = version;
        this.status = status;
        this.licenseValid = licenseValid;
        this.licenseStatus = licenseStatus;
        this.description = description;
    }

    // Getters and Setters
    public String getPluginId() {
        return pluginId;
    }

    public void setPluginId(String pluginId) {
        this.pluginId = pluginId;
    }

    public String getPluginName() {
        return pluginName;
    }

    public void setPluginName(String pluginName) {
        this.pluginName = pluginName;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public boolean isLicenseValid() {
        return licenseValid;
    }

    public void setLicenseValid(boolean licenseValid) {
        this.licenseValid = licenseValid;
    }

    public String getLicenseStatus() {
        return licenseStatus;
    }

    public void setLicenseStatus(String licenseStatus) {
        this.licenseStatus = licenseStatus;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
