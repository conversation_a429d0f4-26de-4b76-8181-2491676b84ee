package com.fasnote.alm.plugin.manage.web.dto;

import java.util.Date;
import java.util.Map;

/**
 * 许可证信息DTO
 */
public class LicenseInfoDto {
    private String pluginId;
    private boolean valid;
    private String status;
    private String licenseType;
    private Date issueDate;
    private Date expiryDate;
    private String licensee;
    private Map<String, Object> features;
    private String errorMessage;

    public LicenseInfoDto() {}

    public LicenseInfoDto(String pluginId, boolean valid, String status, String licenseType,
                         Date issueDate, Date expiryDate, String licensee, 
                         Map<String, Object> features, String errorMessage) {
        this.pluginId = pluginId;
        this.valid = valid;
        this.status = status;
        this.licenseType = licenseType;
        this.issueDate = issueDate;
        this.expiryDate = expiryDate;
        this.licensee = licensee;
        this.features = features;
        this.errorMessage = errorMessage;
    }

    // Getters and Setters
    public String getPluginId() {
        return pluginId;
    }

    public void setPluginId(String pluginId) {
        this.pluginId = pluginId;
    }

    public boolean isValid() {
        return valid;
    }

    public void setValid(boolean valid) {
        this.valid = valid;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getLicenseType() {
        return licenseType;
    }

    public void setLicenseType(String licenseType) {
        this.licenseType = licenseType;
    }

    public Date getIssueDate() {
        return issueDate;
    }

    public void setIssueDate(Date issueDate) {
        this.issueDate = issueDate;
    }

    public Date getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(Date expiryDate) {
        this.expiryDate = expiryDate;
    }

    public String getLicensee() {
        return licensee;
    }

    public void setLicensee(String licensee) {
        this.licensee = licensee;
    }

    public Map<String, Object> getFeatures() {
        return features;
    }

    public void setFeatures(Map<String, Object> features) {
        this.features = features;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}
