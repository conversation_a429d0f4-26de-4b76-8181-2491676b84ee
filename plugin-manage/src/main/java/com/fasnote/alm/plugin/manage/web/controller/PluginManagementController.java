package com.fasnote.alm.plugin.manage.web.controller;

import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fasnote.alm.plugin.manage.web.dto.LicenseInfoDto;
import com.fasnote.alm.plugin.manage.web.dto.LicenseUpdateRequest;
import com.fasnote.alm.plugin.manage.web.dto.LicenseUpdateResponse;
import com.fasnote.alm.plugin.manage.web.dto.PluginInfoDto;
import com.fasnote.alm.plugin.manage.web.service.PluginManagementService;

/**
 * 插件管理REST API控制器
 * 提供插件和许可证管理的HTTP接口
 */
@RestController
@RequestMapping("/")
public class PluginManagementController {

    private static final Logger logger = LoggerFactory.getLogger(PluginManagementController.class);
    private static final String LOG_PREFIX = "[PluginManagementController] ";

    private final PluginManagementService pluginManagementService;

    public PluginManagementController(PluginManagementService pluginManagementService) {
        this.pluginManagementService = pluginManagementService;
    }

    /**
     * 获取所有已注册的插件列表
     * GET /api/plugins
     */
    @GetMapping("/plugins")
    public ResponseEntity<List<PluginInfoDto>> getAllPlugins() {
        logger.info(LOG_PREFIX + "获取所有插件列表");
        
        try {
            List<PluginInfoDto> plugins = pluginManagementService.getAllPlugins();
            logger.info(LOG_PREFIX + "成功返回 " + plugins.size() + " 个插件信息");
            return ResponseEntity.ok(plugins);
            
        } catch (Exception e) {
            logger.error(LOG_PREFIX + "获取插件列表失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 获取指定插件的许可证信息
     * GET /api/plugins/{pluginId}/license
     */
    @GetMapping("/plugins/{pluginId}/license")
    public ResponseEntity<LicenseInfoDto> getPluginLicense(@PathVariable String pluginId) {
        logger.info(LOG_PREFIX + "获取插件许可证信息: " + pluginId);
        
        try {
            LicenseInfoDto licenseInfo = pluginManagementService.getPluginLicense(pluginId);
            
            if (licenseInfo != null) {
                return ResponseEntity.ok(licenseInfo);
            } else {
                return ResponseEntity.notFound().build();
            }
            
        } catch (Exception e) {
            logger.error(LOG_PREFIX + "获取许可证信息失败: " + pluginId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 更新插件许可证
     * POST /api/plugins/{pluginId}/license
     */
    @PostMapping("/plugins/{pluginId}/license")
    public ResponseEntity<LicenseUpdateResponse> updatePluginLicense(
            @PathVariable String pluginId,
            @RequestBody LicenseUpdateRequest request) {
        
        logger.info(LOG_PREFIX + "更新插件许可证: " + pluginId);
        
        try {
            LicenseUpdateResponse response = pluginManagementService.updatePluginLicense(pluginId, request);
            
            if (response.isSuccess()) {
                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.badRequest().body(response);
            }
            
        } catch (Exception e) {
            logger.error(LOG_PREFIX + "更新许可证失败: " + pluginId, e);
            
            LicenseUpdateResponse errorResponse = new LicenseUpdateResponse(
                false, 
                "服务器内部错误: " + e.getMessage(), 
                "INTERNAL_ERROR"
            );
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 获取框架统计信息
     * GET /api/framework/statistics
     */
    @GetMapping("/framework/statistics")
    public ResponseEntity<Map<String, Object>> getFrameworkStatistics() {
        logger.info(LOG_PREFIX + "获取框架统计信息");
        
        try {
            Map<String, Object> statistics = pluginManagementService.getFrameworkStatistics();
            return ResponseEntity.ok(statistics);
            
        } catch (Exception e) {
            logger.error(LOG_PREFIX + "获取框架统计信息失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 刷新所有插件的许可证状态
     * POST /api/framework/refresh
     */
    @PostMapping("/framework/refresh")
    public ResponseEntity<String> refreshAllLicenses() {
        logger.info(LOG_PREFIX + "刷新所有许可证状态");
        
        try {
            pluginManagementService.refreshAllLicenses();
            return ResponseEntity.ok("许可证状态刷新成功");
            
        } catch (Exception e) {
            logger.error(LOG_PREFIX + "刷新许可证状态失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("刷新失败: " + e.getMessage());
        }
    }

    /**
     * 验证插件许可证
     * GET /api/plugins/{pluginId}/validate
     */
    @GetMapping("/plugins/{pluginId}/validate")
    public ResponseEntity<Map<String, Object>> validatePluginLicense(@PathVariable String pluginId) {
        logger.info(LOG_PREFIX + "验证插件许可证: " + pluginId);
        
        try {
            boolean isValid = pluginManagementService.validatePluginLicense(pluginId);
            
            Map<String, Object> result = new java.util.HashMap<>();
            result.put("pluginId", pluginId);
            result.put("valid", isValid);
            result.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            logger.error(LOG_PREFIX + "验证许可证失败: " + pluginId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 健康检查端点
     * GET /api/health
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new java.util.HashMap<>();
        health.put("status", "UP");
        health.put("timestamp", System.currentTimeMillis());
        health.put("service", "Plugin Management API");
        
        return ResponseEntity.ok(health);
    }
}
