package com.fasnote.alm.plugin.manage.audit;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicBoolean;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 审计日志记录器
 * 负责记录许可证验证框架的所有重要操作和事件
 * 
 * 特性：
 * 1. 异步日志记录
 * 2. 多级别日志支持
 * 3. 文件滚动
 * 4. 线程安全
 * 5. 性能优化
 */
public class AuditLogger {
    
    private static final Logger logger = LoggerFactory.getLogger(AuditLogger.class);
    
    private static final DateTimeFormatter TIMESTAMP_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
    private static final String LOG_FILE_PREFIX = "license-audit";
    private static final String LOG_FILE_EXTENSION = ".log";
    private static final int MAX_QUEUE_SIZE = 10000;
    private static final long MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
    
    // 日志级别
    public enum LogLevel {
        DEBUG(0, "DEBUG"),
        INFO(1, "INFO"),
        WARN(2, "WARN"),
        ERROR(3, "ERROR");
        
        private final int level;
        private final String name;
        
        LogLevel(int level, String name) {
            this.level = level;
            this.name = name;
        }
        
        public int getLevel() {
            return level;
        }
        
        public String getName() {
            return name;
        }
    }
    
    // 日志条目
    private static class LogEntry {
        private final LocalDateTime timestamp;
        private final LogLevel level;
        private final String message;
        private final Throwable throwable;
        
        public LogEntry(LogLevel level, String message, Throwable throwable) {
            this.timestamp = LocalDateTime.now();
            this.level = level;
            this.message = message;
            this.throwable = throwable;
        }
        
        public String format() {
            StringBuilder sb = new StringBuilder();
            sb.append("[").append(timestamp.format(TIMESTAMP_FORMATTER)).append("] ");
            sb.append("[").append(level.getName()).append("] ");
            sb.append(message);
            
            if (throwable != null) {
                sb.append("\n").append(getStackTrace(throwable));
            }
            
            return sb.toString();
        }
        
        private String getStackTrace(Throwable throwable) {
            java.io.StringWriter sw = new java.io.StringWriter();
            java.io.PrintWriter pw = new java.io.PrintWriter(sw);
            throwable.printStackTrace(pw);
            return sw.toString();
        }
    }
    
    // 日志队列
    private final BlockingQueue<LogEntry> logQueue = new LinkedBlockingQueue<>(MAX_QUEUE_SIZE);
    
    // 异步处理线程
    private final ExecutorService logProcessor = Executors.newSingleThreadExecutor(r -> {
        Thread t = new Thread(r, "LicenseAuditLogger");
        t.setDaemon(true);
        return t;
    });
    
    // 当前日志级别
    private volatile LogLevel currentLogLevel = LogLevel.DEBUG;
    
    // 日志文件相关
    private String logDirectory;
    private File currentLogFile;
    private PrintWriter logWriter;
    
    // 运行状态
    private final AtomicBoolean running = new AtomicBoolean(true);
    
    // 单例实例
    private static volatile AuditLogger instance;
    
    /**
     * 私有构造函数
     */
    private AuditLogger() {
        initializeLogDirectory();
        initialize();
        startLogProcessor();
    }
    
    /**
     * 获取单例实例
     */
    public static AuditLogger getInstance() {
        if (instance == null) {
            synchronized (AuditLogger.class) {
                if (instance == null) {
                    instance = new AuditLogger();
                }
            }
        }
        return instance;
    }

    /**
     * 初始化日志目录路径
     * 通过类路径相对定位确定项目根目录
     */
    private void initializeLogDirectory() {
        try {
            // 获取当前类的位置
            String classPath = AuditLogger.class.getProtectionDomain()
                    .getCodeSource().getLocation().getPath();

            // 解码路径中的特殊字符
            classPath = java.net.URLDecoder.decode(classPath, "UTF-8");

            File classFile = new File(classPath);
            File projectRoot = null;

            // 如果是在开发环境中（classes目录）
            if (classFile.isDirectory()) {
                projectRoot = classFile;
            }
            // 如果是在JAR文件中
            else if (classPath.endsWith(".jar")) {
                // 从JAR文件所在目录向上找项目根目录
                projectRoot = classFile.getParentFile();
                // 如果JAR在target目录中，再向上一级
                if (projectRoot.getName().equals("target")) {
                    projectRoot = projectRoot.getParentFile();
                }
            }
            // 其他情况，尝试从当前工作目录开始
            else {
                projectRoot = new File(System.getProperty("user.dir"));
            }

            // 确保找到了有效的项目根目录
            if (projectRoot != null && projectRoot.exists()) {
                logDirectory = new File(projectRoot, "logs").getAbsolutePath();
            } else {
                // 如果无法确定项目根目录，使用当前工作目录下的logs
                logDirectory = new File(System.getProperty("user.dir"), "logs").getAbsolutePath();
            }

            logger.debug("日志目录已设置为: {}", logDirectory);

        } catch (Exception e) {
            // 如果出现异常，使用默认的相对路径
            logDirectory = new File(System.getProperty("user.dir"), "logs").getAbsolutePath();
            logger.warn("初始化日志目录路径时出现异常，使用默认路径: {}", logDirectory, e);
        }
    }
    
    /**
     * 初始化日志记录器
     */
    private void initialize() {
        try {
            // 创建日志目录
            File logDir = new File(logDirectory);
            if (!logDir.exists()) {
                logDir.mkdirs();
            }
            
            // 创建日志文件
            createNewLogFile();
            
        } catch (Exception e) {
            logger.error("初始化审计日志记录器失败", e);
        }
    }
    
    /**
     * 创建新的日志文件
     */
    private void createNewLogFile() throws IOException {
        // 关闭当前文件
        if (logWriter != null) {
            logWriter.close();
        }
        
        // 创建新文件
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd-HHmmss"));
        String fileName = LOG_FILE_PREFIX + "-" + timestamp + LOG_FILE_EXTENSION;
        currentLogFile = new File(logDirectory, fileName);
        
        logWriter = new PrintWriter(new FileWriter(currentLogFile, true));
        
        // 写入文件头
        logWriter.println("=== License Audit Log Started at " + 
                         LocalDateTime.now().format(TIMESTAMP_FORMATTER) + " ===");
        logWriter.flush();
    }
    
    /**
     * 启动日志处理器
     */
    private void startLogProcessor() {
        logProcessor.submit(() -> {
            while (running.get() || !logQueue.isEmpty()) {
                try {
                    LogEntry entry = logQueue.take();
                    writeLogEntry(entry);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    logger.error("处理日志条目错误", e);
                }
            }
        });
    }
    
    /**
     * 写入日志条目
     */
    private void writeLogEntry(LogEntry entry) {
        try {
            // 检查文件大小，如果超过限制则创建新文件
            if (currentLogFile.length() > MAX_FILE_SIZE) {
                createNewLogFile();
            }
            
            // 写入日志
            logWriter.println(entry.format());
            logWriter.flush();
            
            // 同时输出到控制台（可配置）
            if (shouldOutputToConsole(entry.level)) {
                logger.debug(entry.format());
            }
            
        } catch (Exception e) {
            logger.error("写入日志条目失败", e);
        }
    }
    
    /**
     * 检查是否应该输出到控制台
     */
    private boolean shouldOutputToConsole(LogLevel level) {
        // 所有级别的日志都输出到控制台（用于调试）
        return true;
    }
    
    /**
     * 记录调试日志
     */
    public void debug(String message) {
        log(LogLevel.DEBUG, message, null);
    }

    /**
     * 记录调试日志（带异常）
     */
    public void debug(String message, Throwable throwable) {
        log(LogLevel.DEBUG, message, throwable);
    }
    
    /**
     * 记录信息日志
     */
    public void info(String message) {
        log(LogLevel.INFO, message, null);
    }
    
    /**
     * 记录警告日志
     */
    public void warn(String message) {
        log(LogLevel.WARN, message, null);
    }
    
    /**
     * 记录警告日志（带异常）
     */
    public void warn(String message, Throwable throwable) {
        log(LogLevel.WARN, message, throwable);
    }
    
    /**
     * 记录错误日志
     */
    public void error(String message) {
        log(LogLevel.ERROR, message, null);
    }
    
    /**
     * 记录错误日志（带异常）
     */
    public void error(String message, Throwable throwable) {
        log(LogLevel.ERROR, message, throwable);
    }
    
    /**
     * 记录日志
     */
    private void log(LogLevel level, String message, Throwable throwable) {
        // 检查日志级别
        if (level.getLevel() < currentLogLevel.getLevel()) {
            return;
        }
        
        // 创建日志条目
        LogEntry entry = new LogEntry(level, message, throwable);
        
        // 添加到队列
        if (!logQueue.offer(entry)) {
            // 队列满了，直接输出到控制台
            logger.warn("日志队列已满，丢弃日志条目: {}", message);
        }
    }
    
    /**
     * 记录许可证验证事件
     */
    public void logLicenseValidation(String pluginId, String operation, boolean success, String details) {
        String message = String.format("许可证验证 [插件=%s, 操作=%s, 结果=%s, 详情=%s]", 
                                      pluginId, operation, success ? "成功" : "失败", details);
        
        if (success) {
            info(message);
        } else {
            warn(message);
        }
    }
    
    /**
     * 记录插件操作事件
     */
    public void logPluginOperation(String pluginId, String operation, boolean success, String details) {
        String message = String.format("插件操作 [插件=%s, 操作=%s, 结果=%s, 详情=%s]", 
                                      pluginId, operation, success ? "成功" : "失败", details);
        
        if (success) {
            info(message);
        } else {
            error(message);
        }
    }
    
    /**
     * 记录安全事件
     */
    public void logSecurityEvent(String eventType, String details, boolean isAlert) {
        String message = String.format("安全事件 [类型=%s, 详情=%s]", eventType, details);
        
        if (isAlert) {
            error(message);
        } else {
            warn(message);
        }
    }
    
    /**
     * 设置日志级别
     */
    public void setLogLevel(LogLevel level) {
        this.currentLogLevel = level;
        info("日志级别已设置为: " + level.getName());
    }
    
    /**
     * 设置日志目录
     */
    public void setLogDirectory(String directory) {
        this.logDirectory = directory;
        info("日志目录已设置为: " + directory);
        
        try {
            initialize(); // 重新初始化
        } catch (Exception e) {
            error("重新初始化日志目录失败", e);
        }
    }
    
    /**
     * 获取当前日志级别
     */
    public LogLevel getCurrentLogLevel() {
        return currentLogLevel;
    }
    
    /**
     * 获取日志队列大小
     */
    public int getQueueSize() {
        return logQueue.size();
    }
    
    /**
     * 刷新日志
     */
    public void flush() {
        if (logWriter != null) {
            logWriter.flush();
        }
    }
    
    /**
     * 关闭日志记录器
     */
    public void shutdown() {
        info("关闭审计日志记录器");
        
        running.set(false);
        
        // 等待队列中的日志处理完成
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 关闭线程池
        logProcessor.shutdown();
        
        // 关闭文件
        if (logWriter != null) {
            logWriter.close();
        }
    }
}
