package com.fasnote.alm.plugin.manage.config;

import java.util.Arrays;
import java.util.List;

/**
 * 许可证配置属性
 */
public class LicenseProperties {
    
    // 存储配置
    private String storageDirectory = "/opt/licenses";
    private String backupDirectory = "/opt/licenses/backup";
    
    // 缓存配置
    private int cacheSize = 100;
    private long cacheTtl = 3600; // 秒
    
    // 安全配置
    private boolean validationEnabled = true;
    private boolean machineCodeBinding = true;
    
    // 功能模式配置
    private List<String> strictModeFeatures = Arrays.asList("PremiumAI", "EnterpriseIntegration");
    private List<String> lenientModeFeatures = Arrays.asList("FeishuIntegration", "AdvancedReporting");
    private List<String> basicModeFeatures = Arrays.asList("UIServices", "BasicReporting");
    
    // 默认降级策略
    private String defaultFallback = "PROXY";
    private String uiFallback = "STUB";
    
    // 监控配置
    private boolean metricsEnabled = true;
    private boolean auditEnabled = true;
    
    // Getters and Setters
    public String getStorageDirectory() {
        return storageDirectory;
    }
    
    public void setStorageDirectory(String storageDirectory) {
        this.storageDirectory = storageDirectory;
    }
    
    public String getBackupDirectory() {
        return backupDirectory;
    }
    
    public void setBackupDirectory(String backupDirectory) {
        this.backupDirectory = backupDirectory;
    }
    
    public int getCacheSize() {
        return cacheSize;
    }
    
    public void setCacheSize(int cacheSize) {
        this.cacheSize = cacheSize;
    }
    
    public long getCacheTtl() {
        return cacheTtl;
    }
    
    public void setCacheTtl(long cacheTtl) {
        this.cacheTtl = cacheTtl;
    }
    
    public boolean isValidationEnabled() {
        return validationEnabled;
    }
    
    public void setValidationEnabled(boolean validationEnabled) {
        this.validationEnabled = validationEnabled;
    }
    
    public boolean isMachineCodeBinding() {
        return machineCodeBinding;
    }
    
    public void setMachineCodeBinding(boolean machineCodeBinding) {
        this.machineCodeBinding = machineCodeBinding;
    }
    
    public List<String> getStrictModeFeatures() {
        return strictModeFeatures;
    }
    
    public void setStrictModeFeatures(List<String> strictModeFeatures) {
        this.strictModeFeatures = strictModeFeatures;
    }
    
    public List<String> getLenientModeFeatures() {
        return lenientModeFeatures;
    }
    
    public void setLenientModeFeatures(List<String> lenientModeFeatures) {
        this.lenientModeFeatures = lenientModeFeatures;
    }
    
    public List<String> getBasicModeFeatures() {
        return basicModeFeatures;
    }
    
    public void setBasicModeFeatures(List<String> basicModeFeatures) {
        this.basicModeFeatures = basicModeFeatures;
    }
    
    public String getDefaultFallback() {
        return defaultFallback;
    }
    
    public void setDefaultFallback(String defaultFallback) {
        this.defaultFallback = defaultFallback;
    }
    
    public String getUiFallback() {
        return uiFallback;
    }
    
    public void setUiFallback(String uiFallback) {
        this.uiFallback = uiFallback;
    }
    
    public boolean isMetricsEnabled() {
        return metricsEnabled;
    }
    
    public void setMetricsEnabled(boolean metricsEnabled) {
        this.metricsEnabled = metricsEnabled;
    }
    
    public boolean isAuditEnabled() {
        return auditEnabled;
    }
    
    public void setAuditEnabled(boolean auditEnabled) {
        this.auditEnabled = auditEnabled;
    }
}