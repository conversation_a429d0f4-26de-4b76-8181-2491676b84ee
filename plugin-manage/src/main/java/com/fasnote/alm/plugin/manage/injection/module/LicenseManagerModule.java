package com.fasnote.alm.plugin.manage.injection.module;

import org.osgi.framework.BundleContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.injection.api.IBinder;
import com.fasnote.alm.injection.api.IModule;
import com.fasnote.alm.injection.api.IServiceProvider;
import com.fasnote.alm.plugin.manage.core.LicenseManager;

/**
 * LicenseManager 依赖注入配置模块
 * 负责配置 LicenseManager 的依赖注入
 */
public class LicenseManagerModule implements IModule {
    
    private static final Logger logger = LoggerFactory.getLogger(LicenseManagerModule.class);
    
    private final BundleContext bundleContext;
    
    /**
     * 构造函数
     * 
     * @param bundleContext OSGi Bundle上下文
     */
    public LicenseManagerModule(BundleContext bundleContext) {
        if (bundleContext == null) {
            throw new IllegalArgumentException("BundleContext不能为null");
        }
        this.bundleContext = bundleContext;
        logger.info("LicenseManagerModule创建完成");
    }
    
    @Override
    public void configure(IBinder binder) {
        logger.info("开始配置LicenseManager依赖注入...");
        
        // 注册 LicenseManager 为单例服务
        binder.bind(LicenseManager.class)
              .toProvider(new LicenseManagerProvider())
              .asSingleton()
              .build();
        
        logger.info("LicenseManager依赖注入配置完成");
    }
    
    /**
     * LicenseManager 服务提供者
     */
    private class LicenseManagerProvider implements IServiceProvider<LicenseManager> {
        
        @Override
        public LicenseManager provide(com.fasnote.alm.injection.api.IInjectionContext context) {
            logger.debug("创建LicenseManager实例...");
            
            try {
                LicenseManager licenseManager = new LicenseManager(bundleContext);
                logger.info("LicenseManager实例创建成功");
                return licenseManager;
            } catch (Exception e) {
                logger.error("创建LicenseManager实例失败", e);
                throw new RuntimeException("无法创建LicenseManager实例", e);
            }
        }
    }
}
