package com.fasnote.alm.plugin.manage.web.service;

import java.util.List;
import java.util.Map;

import com.fasnote.alm.plugin.manage.web.dto.LicenseInfoDto;
import com.fasnote.alm.plugin.manage.web.dto.LicenseUpdateRequest;
import com.fasnote.alm.plugin.manage.web.dto.LicenseUpdateResponse;
import com.fasnote.alm.plugin.manage.web.dto.PluginInfoDto;

/**
 * 插件管理服务接口
 * 提供插件和许可证管理的业务逻辑
 */
public interface PluginManagementService {

    /**
     * 获取所有已注册的插件列表
     * 
     * @return 插件信息列表
     */
    List<PluginInfoDto> getAllPlugins();

    /**
     * 获取指定插件的许可证信息
     * 
     * @param pluginId 插件ID
     * @return 许可证信息
     */
    LicenseInfoDto getPluginLicense(String pluginId);

    /**
     * 更新插件许可证
     * 
     * @param pluginId 插件ID
     * @param request 许可证更新请求
     * @return 更新结果
     */
    LicenseUpdateResponse updatePluginLicense(String pluginId, LicenseUpdateRequest request);

    /**
     * 获取框架统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> getFrameworkStatistics();

    /**
     * 刷新所有插件的许可证状态
     */
    void refreshAllLicenses();

    /**
     * 验证插件许可证
     * 
     * @param pluginId 插件ID
     * @return 验证结果
     */
    boolean validatePluginLicense(String pluginId);
}
