package com.fasnote.alm.plugin.manage.model;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

import com.fasnote.alm.plugin.manage.util.JsonUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 插件许可证模型
 * 表示一个插件的许可证信息，包含所有验证所需的数据
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class PluginLicense {
    
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    // 基本信息
    private String pluginId;
    private String productName;
    private String version;
    private String licenseType;
    private String issuer;
    
    // 时间限制
    private LocalDateTime issueDate;
    private LocalDateTime effectiveDate;
    private LocalDateTime expiryDate;
    
    // 安全信息
    private String machineCode;
    private String signature;
    private String contentHash;
    
    // 功能限制
    private Map<String, Object> features;
    private Map<String, Object> limitations;
    
    // 用户信息
    private String licensedTo;
    private String organization;
    private int maxUsers;
    
    // 扩展属性
    private Map<String, Object> customProperties;
    
    // 原始许可证数据
    private String rawLicenseData;

    // 许可证文件路径（用于crypto模块的文件路径接口）
    private String licenseFilePath;
    
    // 加密的实现类数据
    private byte[] encryptedClassData;
    
    // 服务接口和实现类的映射
    private Map<String, String> serviceMappings;
    
    /**
     * 无参构造函数（用于 Jackson 反序列化）
     */
    public PluginLicense() {
        this.features = new HashMap<>();
        this.limitations = new HashMap<>();
        this.customProperties = new HashMap<>();
        this.serviceMappings = new HashMap<>();
    }

    /**
     * 构造函数
     */
    public PluginLicense(String pluginId, String rawLicenseData) {
        this();
        this.pluginId = pluginId;
        this.rawLicenseData = rawLicenseData;

        // 解析许可证数据
        parseLicenseData(rawLicenseData);
    }
    
    /**
     * 解析许可证数据
     * 
     * @param licenseData 许可证数据（JSON格式）
     */
    private void parseLicenseData(String licenseData) {
        try {
            Map<String, Object> data = JsonUtil.parseJson(licenseData);
            
            // 解析基本信息
            this.productName = (String) data.get("productName");
            this.version = (String) data.get("version");
            this.licenseType = (String) data.get("licenseType");
            this.issuer = (String) data.get("issuer");
            
            // 解析时间信息
            String issueDateStr = (String) data.get("issueDate");
            if (issueDateStr != null) {
                this.issueDate = LocalDateTime.parse(issueDateStr, DATE_FORMATTER);
            }
            
            String effectiveDateStr = (String) data.get("effectiveDate");
            if (effectiveDateStr != null) {
                this.effectiveDate = LocalDateTime.parse(effectiveDateStr, DATE_FORMATTER);
            }
            
            String expiryDateStr = (String) data.get("expiryDate");
            if (expiryDateStr != null) {
                this.expiryDate = LocalDateTime.parse(expiryDateStr, DATE_FORMATTER);
            }
            
            // 解析安全信息
            this.machineCode = (String) data.get("machineCode");
            this.signature = (String) data.get("signature");
            this.contentHash = (String) data.get("contentHash");
            
            // 解析用户信息
            this.licensedTo = (String) data.get("licensedTo");
            this.organization = (String) data.get("organization");
            Object maxUsersObj = data.get("maxUsers");
            if (maxUsersObj instanceof Number) {
                this.maxUsers = ((Number) maxUsersObj).intValue();
            }
            
            // 解析功能和限制
            @SuppressWarnings("unchecked")
            Map<String, Object> featuresMap = (Map<String, Object>) data.get("features");
            if (featuresMap != null) {
                this.features.putAll(featuresMap);
            }
            
            @SuppressWarnings("unchecked")
            Map<String, Object> limitationsMap = (Map<String, Object>) data.get("limitations");
            if (limitationsMap != null) {
                this.limitations.putAll(limitationsMap);
            }
            
            // 解析自定义属性
            @SuppressWarnings("unchecked")
            Map<String, Object> customPropsMap = (Map<String, Object>) data.get("customProperties");
            if (customPropsMap != null) {
                this.customProperties.putAll(customPropsMap);
            }
            
            // 解析加密的实现类数据
            String encryptedClassDataStr = (String) data.get("encryptedClassData");
            if (encryptedClassDataStr != null) {
                // 假设是Base64编码的字节数据
                this.encryptedClassData = java.util.Base64.getDecoder().decode(encryptedClassDataStr);
            }
            
            // 解析服务映射
            @SuppressWarnings("unchecked")
            Map<String, String> serviceMappingsMap = (Map<String, String>) data.get("serviceMappings");
            if (serviceMappingsMap != null) {
                this.serviceMappings.putAll(serviceMappingsMap);
            }
            
        } catch (Exception e) {
            throw new IllegalArgumentException("许可证数据解析失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 检查许可证是否已过期
     * 
     * @return 是否过期
     */
    public boolean isExpired() {
        if (expiryDate == null) {
            return false; // 永久许可证
        }
        return LocalDateTime.now().isAfter(expiryDate);
    }
    
    /**
     * 检查许可证是否已生效
     * 
     * @return 是否已生效
     */
    public boolean isEffective() {
        if (effectiveDate == null) {
            return true; // 立即生效
        }
        return LocalDateTime.now().isAfter(effectiveDate);
    }
    
    /**
     * 检查许可证是否有效
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        return isEffective() && !isExpired();
    }
    
    /**
     * 检查是否支持指定功能
     * 
     * @param featureName 功能名称
     * @return 是否支持
     */
    public boolean hasFeature(String featureName) {
        Object feature = features.get(featureName);
        if (feature instanceof Boolean) {
            return (Boolean) feature;
        }
        return feature != null;
    }
    
    /**
     * 获取功能的限制值
     * 
     * @param featureName 功能名称
     * @return 限制值
     */
    public Object getFeatureLimit(String featureName) {
        return limitations.get(featureName);
    }
    
    /**
     * 获取用于签名验证的内容
     * 排除签名字段本身
     * 
     * @return 签名内容
     */
    public String getContentForSignature() {
        StringBuilder content = new StringBuilder();
        content.append("pluginId=").append(pluginId);
        content.append("&productName=").append(productName);
        content.append("&version=").append(version);
        content.append("&licenseType=").append(licenseType);
        content.append("&issuer=").append(issuer);
        
        if (issueDate != null) {
            content.append("&issueDate=").append(issueDate.format(DATE_FORMATTER));
        }
        if (effectiveDate != null) {
            content.append("&effectiveDate=").append(effectiveDate.format(DATE_FORMATTER));
        }
        if (expiryDate != null) {
            content.append("&expiryDate=").append(expiryDate.format(DATE_FORMATTER));
        }
        
        if (machineCode != null) {
            content.append("&machineCode=").append(machineCode);
        }
        if (licensedTo != null) {
            content.append("&licensedTo=").append(licensedTo);
        }
        if (organization != null) {
            content.append("&organization=").append(organization);
        }
        
        content.append("&maxUsers=").append(maxUsers);
        
        // 添加功能和限制信息
        content.append("&features=").append(JsonUtil.toJson(features));
        content.append("&limitations=").append(JsonUtil.toJson(limitations));
        
        return content.toString();
    }
    
    /**
     * 获取用于哈希计算的内容
     * 完全复制 license-builder 的哈希计算逻辑
     *
     * @return 哈希内容
     */
    public String getContentForHash() {
        // 重建与 license-builder 生成时完全相同的 JSON Map
        Map<String, Object> json = new java.util.HashMap<>();

        // 按照 license-builder 的 createPluginLicenseJson 方法重建 JSON
        if (pluginId != null) json.put("pluginId", pluginId);
        if (productName != null) json.put("productName", productName);
        if (version != null) json.put("version", version);
        if (licenseType != null) json.put("licenseType", licenseType);
        if (issuer != null) json.put("issuer", issuer);

        if (issueDate != null) json.put("issueDate", issueDate.format(DATE_FORMATTER));
        if (effectiveDate != null) json.put("effectiveDate", effectiveDate.format(DATE_FORMATTER));
        if (expiryDate != null) json.put("expiryDate", expiryDate.format(DATE_FORMATTER));

        if (machineCode != null) json.put("machineCode", machineCode);
        if (signature != null) json.put("signature", signature);
        if (licensedTo != null) json.put("licensedTo", licensedTo);
        if (organization != null) json.put("organization", organization);

        json.put("maxUsers", maxUsers);

        if (features != null) json.put("features", features);
        if (limitations != null) json.put("limitations", limitations);
        if (customProperties != null) json.put("customProperties", customProperties);

        // 注意：serviceMappings 不包含在哈希计算中，因为它是在 contentHash 计算之后添加的

        // 添加加密数据（如果存在）
        if (encryptedClassData != null) {
            json.put("encryptedClassData", java.util.Base64.getEncoder().encodeToString(encryptedClassData));
        }

        // 完全复制 license-builder 的 createContentForHash 逻辑
        StringBuilder content = new StringBuilder();

        json.entrySet().stream()
                .filter(entry -> !"signature".equals(entry.getKey()) &&
                               !"contentHash".equals(entry.getKey()) &&
                               !"digitalSignature".equals(entry.getKey()))
                .sorted(Map.Entry.comparingByKey())
                .forEach(entry -> {
                    content.append(entry.getKey()).append("=").append(entry.getValue()).append(";");
                });

        return content.toString();
    }
    
    // Getter 和 Setter 方法
    
    public String getPluginId() {
        return pluginId;
    }
    
    public void setPluginId(String pluginId) {
        this.pluginId = pluginId;
    }
    
    public String getProductName() {
        return productName;
    }
    
    public void setProductName(String productName) {
        this.productName = productName;
    }
    
    public String getVersion() {
        return version;
    }
    
    public void setVersion(String version) {
        this.version = version;
    }
    
    public String getLicenseType() {
        return licenseType;
    }
    
    public void setLicenseType(String licenseType) {
        this.licenseType = licenseType;
    }
    
    public String getIssuer() {
        return issuer;
    }
    
    public void setIssuer(String issuer) {
        this.issuer = issuer;
    }
    
    public LocalDateTime getIssueDate() {
        return issueDate;
    }

    public void setIssueDate(LocalDateTime issueDate) {
        this.issueDate = issueDate;
    }

    /**
     * 设置签发日期（从字符串）
     * 用于 Jackson 反序列化
     */
    @JsonProperty("issueDate")
    public void setIssueDate(String issueDateStr) {
        if (issueDateStr != null && !issueDateStr.trim().isEmpty()) {
            try {
                this.issueDate = LocalDateTime.parse(issueDateStr, DATE_FORMATTER);
            } catch (Exception e) {
                System.err.println("警告: 无效的 issueDate 格式: " + issueDateStr + ", " + e.getMessage());
                this.issueDate = null;
            }
        } else {
            this.issueDate = null;
        }
    }
    
    public LocalDateTime getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(LocalDateTime effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    /**
     * 设置生效日期（从字符串）
     * 用于 Jackson 反序列化
     */
    @JsonProperty("effectiveDate")
    public void setEffectiveDate(String effectiveDateStr) {
        if (effectiveDateStr != null && !effectiveDateStr.trim().isEmpty()) {
            try {
                this.effectiveDate = LocalDateTime.parse(effectiveDateStr, DATE_FORMATTER);
            } catch (Exception e) {
                System.err.println("警告: 无效的 effectiveDate 格式: " + effectiveDateStr + ", " + e.getMessage());
                this.effectiveDate = null;
            }
        } else {
            this.effectiveDate = null;
        }
    }
    
    public LocalDateTime getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(LocalDateTime expiryDate) {
        this.expiryDate = expiryDate;
    }

    /**
     * 设置过期日期（从字符串）
     * 用于 Jackson 反序列化
     */
    @JsonProperty("expiryDate")
    public void setExpiryDate(String expiryDateStr) {
        if (expiryDateStr != null && !expiryDateStr.trim().isEmpty()) {
            try {
                this.expiryDate = LocalDateTime.parse(expiryDateStr, DATE_FORMATTER);
            } catch (Exception e) {
                System.err.println("警告: 无效的 expiryDate 格式: " + expiryDateStr + ", " + e.getMessage());
                this.expiryDate = null;
            }
        } else {
            this.expiryDate = null;
        }
    }
    
    public String getMachineCode() {
        return machineCode;
    }
    
    public void setMachineCode(String machineCode) {
        this.machineCode = machineCode;
    }
    
    public String getSignature() {
        return signature;
    }
    
    public void setSignature(String signature) {
        this.signature = signature;
    }
    
    public String getContentHash() {
        return contentHash;
    }
    
    public void setContentHash(String contentHash) {
        this.contentHash = contentHash;
    }
    
    public String getLicensedTo() {
        return licensedTo;
    }
    
    public void setLicensedTo(String licensedTo) {
        this.licensedTo = licensedTo;
    }
    
    public String getOrganization() {
        return organization;
    }
    
    public void setOrganization(String organization) {
        this.organization = organization;
    }
    
    public int getMaxUsers() {
        return maxUsers;
    }
    
    public void setMaxUsers(int maxUsers) {
        this.maxUsers = maxUsers;
    }
    
    public Map<String, Object> getFeatures() {
        return features;
    }
    
    public Map<String, Object> getLimitations() {
        return limitations;
    }
    
    public Map<String, Object> getCustomProperties() {
        return customProperties;
    }
    
    public String getRawLicenseData() {
        return rawLicenseData;
    }

    /**
     * 设置原始许可证数据
     *
     * @param rawLicenseData 原始许可证数据
     */
    public void setRawLicenseData(String rawLicenseData) {
        this.rawLicenseData = rawLicenseData;
    }

    /**
     * 获取许可证数据（别名方法）
     *
     * @return 原始许可证数据
     */
    public String getLicenseData() {
        return rawLicenseData;
    }
    
    /**
     * 获取加密的实现类数据
     * 
     * @return 加密的类数据字节数组
     */
    public byte[] getEncryptedClassData() {
        return encryptedClassData;
    }
    
    /**
     * 设置加密的实现类数据
     *
     * @param encryptedClassData 加密的类数据字节数组
     */
    public void setEncryptedClassData(byte[] encryptedClassData) {
        this.encryptedClassData = encryptedClassData;
    }

    /**
     * 设置加密的实现类数据（从 Base64 字符串）
     * 用于 Jackson 反序列化
     *
     * @param encryptedClassDataStr Base64 编码的加密类数据字符串
     */
    @JsonProperty("encryptedClassData")
    public void setEncryptedClassData(String encryptedClassDataStr) {
        if (encryptedClassDataStr != null && !encryptedClassDataStr.trim().isEmpty()) {
            try {
                this.encryptedClassData = java.util.Base64.getDecoder().decode(encryptedClassDataStr);
            } catch (IllegalArgumentException e) {
                // 如果不是有效的 Base64 字符串，记录警告但不抛出异常
                System.err.println("警告: 无效的 Base64 encryptedClassData: " + e.getMessage());
                this.encryptedClassData = null;
            }
        } else {
            this.encryptedClassData = null;
        }
    }
    
    /**
     * 检查是否包含加密的实现类
     * 
     * @return 是否包含加密类
     */
    public boolean hasEncryptedClasses() {
        return encryptedClassData != null && encryptedClassData.length > 0;
    }
    
    /**
     * 获取服务接口和实现类的映射
     * 
     * @return 服务映射
     */
    public Map<String, String> getServiceMappings() {
        return serviceMappings;
    }
    
    /**
     * 设置服务接口和实现类的映射
     * 
     * @param serviceMappings 服务映射
     */
    public void setServiceMappings(Map<String, String> serviceMappings) {
        this.serviceMappings = serviceMappings;
    }
    
    /**
     * 检查是否包含服务映射
     *
     * @return 是否包含服务映射
     */
    public boolean hasServiceMappings() {
        return serviceMappings != null && !serviceMappings.isEmpty();
    }

    /**
     * 获取许可证文件路径
     *
     * @return 许可证文件路径
     */
    public String getLicenseFilePath() {
        return licenseFilePath;
    }

    /**
     * 设置许可证文件路径
     *
     * @param licenseFilePath 许可证文件路径
     */
    public void setLicenseFilePath(String licenseFilePath) {
        this.licenseFilePath = licenseFilePath;
    }
    
    @Override
    public String toString() {
        return String.format("PluginLicense{pluginId='%s', productName='%s', version='%s', " +
                           "licenseType='%s', expiryDate=%s, valid=%s}",
                           pluginId, productName, version, licenseType, expiryDate, isValid());
    }
}
