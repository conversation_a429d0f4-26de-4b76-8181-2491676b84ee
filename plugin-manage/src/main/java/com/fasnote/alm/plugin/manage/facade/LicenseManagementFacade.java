package com.fasnote.alm.plugin.manage.facade;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.plugin.manage.core.LicenseManager;
import com.fasnote.alm.plugin.manage.model.LicenseInfo;
import com.fasnote.alm.plugin.manage.model.PluginLicense;
import com.fasnote.alm.plugin.manage.model.ValidationResult;
import com.fasnote.alm.plugin.manage.security.SecurityValidator;

/**
 * 许可证管理门面
 * 职责：许可证业务操作
 * 
 * 功能：
 * - 许可证注册/验证/查询
 * - 许可证状态管理
 * - 功能权限检查
 * - 委托给LicenseManager
 */
public class LicenseManagementFacade {

    private static final Logger logger = LoggerFactory.getLogger(LicenseManagementFacade.class);

    private final LicenseManager licenseManager;

    public LicenseManagementFacade() {
        // 创建依赖组件
        SecurityValidator securityValidator = new SecurityValidator();

        // 创建 LicenseManager 实例
        this.licenseManager = new LicenseManager(securityValidator);
    }

    /**
     * 构造函数（依赖注入）
     */
    public LicenseManagementFacade(LicenseManager licenseManager) {
        this.licenseManager = licenseManager;
    }
    
    /**
     * 注册插件许可证
     * 
     * @param pluginId 插件ID
     * @param licenseData 许可证数据（加密）
     * @return 注册结果
     */
    public ValidationResult registerPluginLicense(String pluginId, byte[] licenseData) {
        logger.info("注册插件许可证: {}", pluginId);
        return licenseManager.registerPluginLicense(pluginId, licenseData);
    }
    
    /**
     * 验证插件许可证
     * 
     * @param pluginId 插件ID
     * @return 验证结果
     */
    public ValidationResult validatePluginLicense(String pluginId) {
        Optional<PluginLicense> licenseOpt = licenseManager.getPluginLicense(pluginId);
        if (!licenseOpt.isPresent()) {
            return ValidationResult.failure("插件许可证不存在: " + pluginId);
        }
        return licenseManager.validateLicense(licenseOpt.get());
    }
    
    /**
     * 获取插件许可证信息
     * 
     * @param pluginId 插件ID
     * @return 许可证信息
     */
    public LicenseInfo getPluginLicenseInfo(String pluginId) {
        Optional<LicenseInfo> infoOpt = licenseManager.getLicenseInfo(pluginId);
        return infoOpt.orElse(null);
    }
    
    /**
     * 获取所有插件许可证信息
     * 
     * @return 许可证信息列表
     */
    public List<LicenseInfo> getAllPluginLicenses() {
        List<String> pluginIds = licenseManager.getRegisteredPluginIds();
        List<LicenseInfo> licenseInfos = new java.util.ArrayList<>();
        for (String pluginId : pluginIds) {
            Optional<LicenseInfo> infoOpt = licenseManager.getLicenseInfo(pluginId);
            if (infoOpt.isPresent()) {
                licenseInfos.add(infoOpt.get());
            }
        }
        return licenseInfos;
    }
    
    /**
     * 更新插件许可证
     *
     * @param pluginId 插件ID
     * @param licenseContent 许可证内容（Base64编码或原始内容）
     * @return 更新是否成功
     */
    public boolean updatePluginLicense(String pluginId, String licenseContent) {
        logger.info("更新插件许可证: {}", pluginId);

        try {
            // 将许可证内容转换为字节数组
            byte[] licenseData;
            try {
                // 尝试Base64解码
                licenseData = java.util.Base64.getDecoder().decode(licenseContent);
            } catch (IllegalArgumentException e) {
                // 如果不是Base64，直接使用UTF-8编码
                licenseData = licenseContent.getBytes("UTF-8");
            }

            // 注册新的许可证（会覆盖旧的）
            ValidationResult result = licenseManager.registerPluginLicense(pluginId, licenseData);

            if (result.isValid()) {
                logger.info("许可证更新成功: {}", pluginId);
                return true;
            } else {
                logger.warn("许可证更新失败: {}, 原因: {}", pluginId, result.getMessage());
                return false;
            }

        } catch (Exception e) {
            logger.error("更新许可证时发生异常: {}", pluginId, e);
            return false;
        }
    }

    /**
     * 移除插件许可证
     *
     * @param pluginId 插件ID
     */
    public void removePluginLicense(String pluginId) {
        logger.info("移除插件许可证: {}", pluginId);
        licenseManager.removePluginLicense(pluginId);
    }
    
    /**
     * 获取插件许可证状态
     *
     * @param pluginId 插件ID
     * @return 许可证状态描述
     */
    public String getLicenseStatus(String pluginId) {
        try {
            ValidationResult result = validatePluginLicense(pluginId);
            if (result.isValid()) {
                return "有效";
            } else {
                return "无效: " + result.getMessage();
            }
        } catch (Exception e) {
            return "错误: " + e.getMessage();
        }
    }

    /**
     * 获取所有已注册的插件ID
     *
     * @return 插件ID集合
     */
    public Set<String> getRegisteredPluginIds() {
        List<String> pluginIds = licenseManager.getRegisteredPluginIds();
        return new java.util.HashSet<>(pluginIds);
    }

    /**
     * 检查功能是否可用
     *
     * @param featureName 功能名称
     * @return 是否可用
     */
    public boolean isFeatureEnabled(String featureName) {
        return licenseManager.isFeatureEnabled(featureName);
    }
    
    /**
     * 刷新许可证状态
     */
    public void refreshLicenseStatus() {
        logger.info("刷新许可证状态");
        licenseManager.refreshAllLicenses();
    }
    
    /**
     * 获取许可证管理器
     */
    public LicenseManager getLicenseManager() {
        return licenseManager;
    }
}
