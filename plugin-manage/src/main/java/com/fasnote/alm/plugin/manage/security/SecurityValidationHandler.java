package com.fasnote.alm.plugin.manage.security;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.plugin.manage.annotation.FeatureRequired;
import com.fasnote.alm.plugin.manage.annotation.LicenseRequired;
import com.fasnote.alm.plugin.manage.annotation.UserLimitCheck;
import com.fasnote.alm.plugin.manage.model.PluginLicense;
import com.fasnote.alm.plugin.manage.model.ValidationResult;

/**
 * 安全验证动态代理处理器
 * 
 * 拦截被代理对象的方法调用，根据方法上的注解自动进行许可证验证：
 * - @LicenseRequired: 基本许可证验证
 * - @FeatureRequired: 功能权限验证
 * - @UserLimitCheck: 用户数量限制验证
 * 
 * 验证失败时根据注解配置的策略进行处理（抛出异常、返回null、降级服务等）
 */
public class SecurityValidationHandler implements InvocationHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(SecurityValidationHandler.class);
    
    private final Object target;
    private final ISecurityValidator securityValidator;
    private final PluginLicense pluginLicense;
    
    /**
     * 构造函数
     * 
     * @param target 被代理的目标对象
     * @param securityValidator 安全验证器
     * @param pluginLicense 插件许可证
     */
    public SecurityValidationHandler(Object target, ISecurityValidator securityValidator, PluginLicense pluginLicense) {
        this.target = target;
        this.securityValidator = securityValidator;
        this.pluginLicense = pluginLicense;
    }
    
    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        // 检查方法是否需要许可证验证
        if (requiresValidation(method)) {
            // 执行许可证验证
            ValidationResult validationResult = performValidation(method, args);
            
            if (!validationResult.isValid()) {
                // 验证失败，根据策略处理
                return handleValidationFailure(method, validationResult);
            }
        }
        
        // 验证通过或不需要验证，调用原方法
        return method.invoke(target, args);
    }
    
    /**
     * 检查方法是否需要许可证验证
     */
    private boolean requiresValidation(Method method) {
        // 检查方法级别的注解
        if (method.isAnnotationPresent(LicenseRequired.class) ||
            method.isAnnotationPresent(FeatureRequired.class) ||
            method.isAnnotationPresent(UserLimitCheck.class)) {
            return true;
        }
        
        // 检查类级别的注解
        Class<?> declaringClass = method.getDeclaringClass();
        return declaringClass.isAnnotationPresent(LicenseRequired.class) ||
               declaringClass.isAnnotationPresent(FeatureRequired.class) ||
               declaringClass.isAnnotationPresent(UserLimitCheck.class);
    }
    
    /**
     * 执行许可证验证
     */
    private ValidationResult performValidation(Method method, Object[] args) {
        try {
            // 检查许可证是否存在
            if (pluginLicense == null) {
                return ValidationResult.failure("许可证未加载");
            }
            
            // 检查安全验证器是否可用
            if (securityValidator == null || !securityValidator.isInitialized()) {
                logger.warn("SecurityValidator未初始化，跳过许可证验证");
                return ValidationResult.success("SecurityValidator未初始化");
            }
            
            // 1. 执行基本许可证验证（如果需要）
            if (requiresBasicValidation(method)) {
                ValidationResult basicResult = securityValidator.validateLicense(pluginLicense);
                if (!basicResult.isValid()) {
                    return basicResult;
                }
            }
            
            // 2. 执行功能权限验证
            ValidationResult featureResult = validateFeaturePermission(method);
            if (!featureResult.isValid()) {
                return featureResult;
            }
            
            // 3. 执行用户数量限制验证
            ValidationResult userLimitResult = validateUserLimit(method, args);
            if (!userLimitResult.isValid()) {
                return userLimitResult;
            }
            
            return ValidationResult.success("许可证验证通过");
            
        } catch (Exception e) {
            logger.error("许可证验证过程中发生错误", e);
            return ValidationResult.failure("许可证验证失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查是否需要基本许可证验证
     */
    private boolean requiresBasicValidation(Method method) {
        // 检查@LicenseRequired注解
        LicenseRequired licenseRequired = getAnnotation(method, LicenseRequired.class);
        if (licenseRequired != null && licenseRequired.enabled()) {
            return true;
        }
        
        // 检查@FeatureRequired注解的requireBasicValidation属性
        FeatureRequired featureRequired = getAnnotation(method, FeatureRequired.class);
        if (featureRequired != null && featureRequired.enabled() && featureRequired.requireBasicValidation()) {
            return true;
        }
        
        // 检查@UserLimitCheck注解的requireBasicValidation属性
        UserLimitCheck userLimitCheck = getAnnotation(method, UserLimitCheck.class);
        if (userLimitCheck != null && userLimitCheck.enabled() && userLimitCheck.requireBasicValidation()) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 验证功能权限
     */
    private ValidationResult validateFeaturePermission(Method method) {
        FeatureRequired featureRequired = getAnnotation(method, FeatureRequired.class);
        if (featureRequired == null || !featureRequired.enabled()) {
            return ValidationResult.success("无需功能权限验证");
        }
        
        String featureName = featureRequired.value();
        if (featureName == null || featureName.trim().isEmpty()) {
            return ValidationResult.failure("功能名称不能为空");
        }
        
        // 首先检查主功能名称
        ValidationResult result = securityValidator.validateFeaturePermission(pluginLicense, featureName);
        if (result.isValid()) {
            return result;
        }
        
        // 如果主功能验证失败，检查别名
        String[] aliases = featureRequired.aliases();
        if (aliases != null && aliases.length > 0) {
            for (String alias : aliases) {
                if (alias != null && !alias.trim().isEmpty()) {
                    ValidationResult aliasResult = securityValidator.validateFeaturePermission(pluginLicense, alias);
                    if (aliasResult.isValid()) {
                        return aliasResult;
                    }
                }
            }
        }
        
        return result; // 返回原始的验证失败结果
    }
    
    /**
     * 验证用户数量限制
     */
    private ValidationResult validateUserLimit(Method method, Object[] args) {
        UserLimitCheck userLimitCheck = getAnnotation(method, UserLimitCheck.class);
        if (userLimitCheck == null || !userLimitCheck.enabled()) {
            return ValidationResult.success("无需用户数量验证");
        }
        
        // 获取当前用户数量
        int currentUserCount = getCurrentUserCount(userLimitCheck, args);
        if (currentUserCount < 0) {
            logger.warn("无法获取当前用户数量，跳过用户数量验证");
            return ValidationResult.success("无法获取用户数量");
        }
        
        return securityValidator.validateUserLimit(pluginLicense, currentUserCount);
    }
    
    /**
     * 获取当前用户数量
     */
    private int getCurrentUserCount(UserLimitCheck userLimitCheck, Object[] args) {
        UserLimitCheck.UserCountSource source = userLimitCheck.userCountSource();
        
        switch (source) {
            case METHOD_PARAMETER:
                // 从方法参数中查找int类型的参数
                if (args != null) {
                    for (Object arg : args) {
                        if (arg instanceof Integer) {
                            return (Integer) arg;
                        }
                    }
                }
                logger.warn("方法参数中未找到int类型的用户数量参数");
                return -1;
                
            case AUTO_DETECT:
                // TODO: 实现从Polarion系统自动获取用户数量的逻辑
                logger.debug("自动检测用户数量功能尚未实现");
                return -1;
                
            case CUSTOM:
                // TODO: 实现自定义用户数量提供者的逻辑
                logger.debug("自定义用户数量提供者功能尚未实现");
                return -1;
                
            case FIXED_VALUE:
                // 用于测试的固定值
                return 1;
                
            default:
                return -1;
        }
    }
    
    /**
     * 处理验证失败的情况
     */
    private Object handleValidationFailure(Method method, ValidationResult validationResult) throws Throwable {
        LicenseRequired.FailureStrategy strategy = getFailureStrategy(method);
        String errorMessage = getErrorMessage(method, validationResult);
        
        switch (strategy) {
            case THROW_EXCEPTION:
                throw new SecurityException(errorMessage);
                
            case RETURN_NULL:
                logger.warn("许可证验证失败，返回null: {}", errorMessage);
                return null;
                
            case DEGRADED_SERVICE:
                logger.warn("许可证验证失败，执行降级服务: {}", errorMessage);
                // TODO: 实现降级服务逻辑
                return null;
                
            case LOG_AND_CONTINUE:
                logger.warn("许可证验证失败，但继续执行: {}", errorMessage);
                return method.invoke(target);
                
            default:
                throw new SecurityException(errorMessage);
        }
    }
    
    /**
     * 获取失败处理策略
     */
    private LicenseRequired.FailureStrategy getFailureStrategy(Method method) {
        // 优先级：方法级别 > 类级别
        LicenseRequired licenseRequired = method.getAnnotation(LicenseRequired.class);
        if (licenseRequired != null) {
            return licenseRequired.onFailure();
        }
        
        FeatureRequired featureRequired = method.getAnnotation(FeatureRequired.class);
        if (featureRequired != null) {
            return featureRequired.onFailure();
        }
        
        UserLimitCheck userLimitCheck = method.getAnnotation(UserLimitCheck.class);
        if (userLimitCheck != null) {
            return userLimitCheck.onFailure();
        }
        
        // 检查类级别注解
        Class<?> declaringClass = method.getDeclaringClass();
        licenseRequired = declaringClass.getAnnotation(LicenseRequired.class);
        if (licenseRequired != null) {
            return licenseRequired.onFailure();
        }
        
        return LicenseRequired.FailureStrategy.THROW_EXCEPTION; // 默认策略
    }
    
    /**
     * 获取错误消息
     */
    private String getErrorMessage(Method method, ValidationResult validationResult) {
        // 尝试获取自定义错误消息
        String customMessage = getCustomErrorMessage(method);
        if (customMessage != null && !customMessage.trim().isEmpty()) {
            return customMessage;
        }
        
        // 使用验证结果中的消息
        return validationResult.getMessage();
    }
    
    /**
     * 获取自定义错误消息
     */
    private String getCustomErrorMessage(Method method) {
        LicenseRequired licenseRequired = getAnnotation(method, LicenseRequired.class);
        if (licenseRequired != null && !licenseRequired.message().trim().isEmpty()) {
            return licenseRequired.message();
        }
        
        FeatureRequired featureRequired = getAnnotation(method, FeatureRequired.class);
        if (featureRequired != null && !featureRequired.message().trim().isEmpty()) {
            return featureRequired.message();
        }
        
        UserLimitCheck userLimitCheck = getAnnotation(method, UserLimitCheck.class);
        if (userLimitCheck != null && !userLimitCheck.message().trim().isEmpty()) {
            return userLimitCheck.message();
        }
        
        return null;
    }
    
    /**
     * 获取注解（优先方法级别，然后类级别）
     */
    private <T extends java.lang.annotation.Annotation> T getAnnotation(Method method, Class<T> annotationClass) {
        // 先检查方法级别
        T annotation = method.getAnnotation(annotationClass);
        if (annotation != null) {
            return annotation;
        }
        
        // 再检查类级别
        return method.getDeclaringClass().getAnnotation(annotationClass);
    }
}
