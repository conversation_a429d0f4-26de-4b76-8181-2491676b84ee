package com.fasnote.alm.plugin.manage.exception;

/**
 * 加密异常类
 * 用于表示许可证加密/解密相关的异常
 */
public class EncryptionException extends LicenseException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 构造函数
     * 
     * @param message 异常消息
     */
    public EncryptionException(String message) {
        super(message);
    }
    
    /**
     * 构造函数
     * 
     * @param message 异常消息
     * @param cause 原因异常
     */
    public EncryptionException(String message, Throwable cause) {
        super(message, cause);
    }
    
    /**
     * 构造函数
     * 
     * @param message 异常消息
     * @param errorCode 错误码
     */
    public EncryptionException(String message, String errorCode) {
        super(message, errorCode);
    }
    
    /**
     * 构造函数
     * 
     * @param message 异常消息
     * @param errorCode 错误码
     * @param cause 原因异常
     */
    public EncryptionException(String message, String errorCode, Throwable cause) {
        super(message, errorCode, cause);
    }
}
