package com.fasnote.alm.plugin.manage.api;

import com.fasnote.alm.injection.api.IModule;

/**
 * 模块注册服务接口
 * 
 * 提供给业务插件用于注册依赖注入模块的服务接口。
 * 通过这个服务，业务插件可以在启动时将自己的模块注册到许可证管理框架中，
 * 避免循环依赖问题。
 */
public interface IModuleRegistrationService {
    
    /**
     * 注册模块
     * 
     * @param module 要注册的模块
     */
    void registerModule(IModule module);
    
    /**
     * 注销模块
     * 
     * @param moduleName 模块名称
     */
    void unregisterModule(String moduleName);
    
    /**
     * 检查模块是否已注册
     * 
     * @param moduleName 模块名称
     * @return 是否已注册
     */
    default boolean isModuleRegistered(String moduleName) {
        return false;
    }
    
    /**
     * 获取已注册的模块数量
     * 
     * @return 模块数量
     */
    default int getRegisteredModuleCount() {
        return 0;
    }
}
