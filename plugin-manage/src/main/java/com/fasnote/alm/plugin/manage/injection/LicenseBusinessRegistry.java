package com.fasnote.alm.plugin.manage.injection;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import org.osgi.framework.Bundle;
import org.osgi.framework.BundleContext;
import org.osgi.framework.FrameworkUtil;
import org.osgi.framework.wiring.BundleWiring;

import com.fasnote.alm.plugin.manage.annotation.FallbackImplementation;
import com.fasnote.alm.plugin.manage.annotation.LicenseImplementation;
import com.fasnote.alm.plugin.manage.api.ILicenseBusinessRegistry;
import com.polarion.core.util.logging.Logger;

/**
 * 许可证业务注册器实现
 * 
 * 统一管理许可证实现和回退实现的注册，支持：
 * 1. 手动注册（向后兼容）
 * 2. 注解驱动的自动注册
 * 3. 优先级管理
 * 4. 线程安全
 */
public class LicenseBusinessRegistry implements ILicenseBusinessRegistry {
    
    private static final Logger logger = Logger.getLogger(LicenseBusinessRegistry.class);
    
    // 存储接口到许可证实现的映射
    private final Map<Class<?>, Class<?>> licenseImplementationMappings = new ConcurrentHashMap<>();
    
    // 存储接口到回退实现的映射
    private final Map<Class<?>, Class<?>> fallbackImplementationMappings = new ConcurrentHashMap<>();
    
    // 存储命名许可证实现的映射
    private final Map<String, Class<?>> namedLicenseImplementationMappings = new ConcurrentHashMap<>();
    
    // 存储命名回退实现的映射
    private final Map<String, Class<?>> namedFallbackImplementationMappings = new ConcurrentHashMap<>();
    
    // 存储优先级信息
    private final Map<Class<?>, Integer> implementationPriorities = new ConcurrentHashMap<>();
    
    @Override
    public <T> void registerLicenseImplementation(Class<T> serviceInterface, Class<? extends T> licenseImplementation) {
        registerLicenseImplementation(serviceInterface, licenseImplementation, null);
    }
    
    @Override
    public <T> void registerLicenseImplementation(Class<T> serviceInterface, Class<? extends T> licenseImplementation, String name) {
        if (serviceInterface == null || licenseImplementation == null) {
            throw new IllegalArgumentException("服务接口和许可证实现类不能为空");
        }
        
        // 验证实现类确实实现了接口
        if (!serviceInterface.isAssignableFrom(licenseImplementation)) {
            throw new IllegalArgumentException(String.format(
                "许可证实现类 %s 没有实现接口 %s", 
                licenseImplementation.getName(), 
                serviceInterface.getName()
            ));
        }
        
        // 获取优先级
        int priority = calculateImplementationPriority(licenseImplementation);
        implementationPriorities.put(licenseImplementation, priority);
        
        if (name != null && !name.trim().isEmpty()) {
            // 注册命名许可证实现
            String key = serviceInterface.getName() + "#" + name;
            namedLicenseImplementationMappings.put(key, licenseImplementation);
            logger.debug("注册命名许可证实现: " + serviceInterface.getName() + " -> " +
                       licenseImplementation.getName() + " (name=" + name + ", priority=" + priority + ")");
        } else {
            // 注册默认许可证实现
            licenseImplementationMappings.put(serviceInterface, licenseImplementation);
            logger.debug("注册默认许可证实现: " + serviceInterface.getName() + " -> " +
                       licenseImplementation.getName() + " (priority=" + priority + ")");
        }
    }
    
    @Override
    public <T> void registerFallbackImplementation(Class<T> serviceInterface, Class<? extends T> fallbackImplementation) {
        registerFallbackImplementation(serviceInterface, fallbackImplementation, null);
    }
    
    @Override
    public <T> void registerFallbackImplementation(Class<T> serviceInterface, Class<? extends T> fallbackImplementation, String name) {
        if (serviceInterface == null || fallbackImplementation == null) {
            throw new IllegalArgumentException("服务接口和回退实现类不能为空");
        }
        
        // 验证实现类确实实现了接口
        if (!serviceInterface.isAssignableFrom(fallbackImplementation)) {
            throw new IllegalArgumentException(String.format(
                "回退实现类 %s 没有实现接口 %s", 
                fallbackImplementation.getName(), 
                serviceInterface.getName()
            ));
        }
        
        // 获取优先级
        int priority = calculateImplementationPriority(fallbackImplementation);
        implementationPriorities.put(fallbackImplementation, priority);
        
        if (name != null && !name.trim().isEmpty()) {
            // 注册命名回退实现
            String key = serviceInterface.getName() + "#" + name;
            namedFallbackImplementationMappings.put(key, fallbackImplementation);
            logger.debug("注册命名回退实现: " + serviceInterface.getName() + " -> " +
                       fallbackImplementation.getName() + " (name=" + name + ", priority=" + priority + ")");
        } else {
            // 注册默认回退实现
            fallbackImplementationMappings.put(serviceInterface, fallbackImplementation);
            logger.debug("注册默认回退实现: " + serviceInterface.getName() + " -> " +
                       fallbackImplementation.getName() + " (priority=" + priority + ")");
        }
    }
    
    @Override
    public void scanAndRegister(String... packagePaths) {
        if (packagePaths == null || packagePaths.length == 0) {
            logger.warn("扫描包路径为空，跳过自动注册");
            return;
        }
        
        logger.info("开始扫描包路径进行自动注册: " + Arrays.toString(packagePaths));

        for (String packagePath : packagePaths) {
            try {
                scanPackage(packagePath);
            } catch (Exception e) {
                logger.error("扫描包路径失败: " + packagePath, e);
            }
        }

        logger.info("包扫描完成，注册统计: " + getRegistrationStats());
    }
    
    /**
     * 扫描单个包路径
     */
    private void scanPackage(String packagePath) {
        logger.debug("扫描包路径: " + packagePath);

        try {
            // 获取类路径下的所有类
            List<Class<?>> classes = scanClasses(packagePath);

            for (Class<?> clazz : classes) {
                logger.debug("检查类: " + clazz.getName());

                // 检查许可证实现注解
                if (clazz.isAnnotationPresent(LicenseImplementation.class)) {
                    logger.debug("发现许可证实现注解: " + clazz.getName());
                    registerLicenseImplementationFromAnnotation(clazz);
                }

                // 检查回退实现注解
                if (clazz.isAnnotationPresent(FallbackImplementation.class)) {
                    logger.debug("发现回退实现注解: " + clazz.getName());
                    registerFallbackImplementationFromAnnotation(clazz);
                }
            }
        } catch (Exception e) {
            logger.error("扫描包失败: " + packagePath, e);
        }
    }
    
    /**
     * 从注解注册许可证实现
     */
    @SuppressWarnings("unchecked")
    private void registerLicenseImplementationFromAnnotation(Class<?> implementationClass) {
        LicenseImplementation annotation = implementationClass.getAnnotation(LicenseImplementation.class);
        if (annotation == null) {
            return;
        }
        
        try {
            // 推断服务接口
            Class<?> serviceInterface = inferServiceInterface(implementationClass);
            if (serviceInterface != null) {
                registerLicenseImplementation(
                    (Class<Object>) serviceInterface, 
                    (Class<Object>) implementationClass
                );
                logger.info("从注解自动注册许可证实现: " + serviceInterface.getName() + " -> " +
                          implementationClass.getName());
            } else {
                logger.warn("无法推断服务接口，跳过注册: " + implementationClass.getName());
            }
        } catch (Exception e) {
            logger.error("从注解注册许可证实现失败: " + implementationClass.getName(), e);
        }
    }
    
    /**
     * 从注解注册回退实现
     */
    @SuppressWarnings("unchecked")
    private void registerFallbackImplementationFromAnnotation(Class<?> implementationClass) {
        FallbackImplementation annotation = implementationClass.getAnnotation(FallbackImplementation.class);
        if (annotation == null) {
            return;
        }
        
        try {
            Class<?> serviceInterface = annotation.value();
            String name = annotation.name();
            
            if (name.isEmpty()) {
                registerFallbackImplementation(
                    (Class<Object>) serviceInterface, 
                    (Class<Object>) implementationClass
                );
            } else {
                registerFallbackImplementation(
                    (Class<Object>) serviceInterface, 
                    (Class<Object>) implementationClass, 
                    name
                );
            }
            
            logger.info("从注解自动注册回退实现: " + serviceInterface.getName() + " -> " +
                      implementationClass.getName() + " (name=" + (name.isEmpty() ? "default" : name) + ")");
        } catch (Exception e) {
            logger.error("从注解注册回退实现失败: " + implementationClass.getName(), e);
        }
    }
    
    /**
     * 推断服务接口
     * 查找实现类的第一个非Object接口
     */
    private Class<?> inferServiceInterface(Class<?> implementationClass) {
        Class<?>[] interfaces = implementationClass.getInterfaces();
        if (interfaces.length > 0) {
            return interfaces[0]; // 返回第一个接口
        }
        
        // 检查父类的接口
        Class<?> superClass = implementationClass.getSuperclass();
        if (superClass != null && superClass != Object.class) {
            return inferServiceInterface(superClass);
        }
        
        return null;
    }
    
    /**
     * 计算实现类的优先级（基于注解）
     */
    private int calculateImplementationPriority(Class<?> implementationClass) {
        // 检查 LicenseImplementation 注解
        LicenseImplementation licenseAnnotation = implementationClass.getAnnotation(LicenseImplementation.class);
        if (licenseAnnotation != null) {
            return 50; // 许可证实现默认高优先级
        }

        // 检查 FallbackImplementation 注解
        FallbackImplementation fallbackAnnotation = implementationClass.getAnnotation(FallbackImplementation.class);
        if (fallbackAnnotation != null) {
            return fallbackAnnotation.priority();
        }

        return 100; // 默认优先级
    }
    
    /**
     * 扫描类路径下的所有类
     */
    private List<Class<?>> scanClasses(String packagePath) {
        List<Class<?>> classes = new ArrayList<>();

        try {
            // 将包名转换为路径
            String packageDir = packagePath.replace('.', '/');

            // 获取类加载器 - 优先使用能够加载目标包的类加载器
            ClassLoader classLoader = findClassLoaderForPackage(packagePath);
            if (classLoader == null) {
                classLoader = Thread.currentThread().getContextClassLoader();
            }
            if (classLoader == null) {
                classLoader = getClass().getClassLoader();
            }

            // 获取包路径下的资源
            java.util.Enumeration<java.net.URL> resources = classLoader.getResources(packageDir);

            logger.debug("使用类加载器: " + classLoader.getClass().getName() + " 扫描包: " + packagePath);

            while (resources.hasMoreElements()) {
                java.net.URL resource = resources.nextElement();
                logger.debug("发现资源: " + resource.toString());

                if (resource.getProtocol().equals("file")) {
                    // 文件系统中的类
                    scanFileSystemClasses(resource.getFile(), packagePath, classes);
                } else if (resource.getProtocol().equals("jar")) {
                    // JAR 文件中的类
                    scanJarClasses(resource, packagePath, classes);
                } else if (resource.getProtocol().equals("bundleresource")) {
                    // OSGi Bundle 中的类
                    scanBundleClasses(resource, packagePath, classes);
                } else {
                    logger.debug("跳过不支持的协议: " + resource.getProtocol() + " - " + resource);
                }
            }

            logger.debug("包 " + packagePath + " 扫描完成，发现 " + classes.size() + " 个类");

        } catch (Exception e) {
            logger.error("扫描包路径失败: " + packagePath, e);
        }

        return classes;
    }

    /**
     * 查找能够加载指定包的类加载器
     */
    private ClassLoader findClassLoaderForPackage(String packagePath) {
        logger.debug("开始查找包 " + packagePath + " 的类加载器");

        // 在 OSGi 环境中，尝试通过 Bundle 机制找到正确的类加载器
        try {
            Bundle currentBundle = FrameworkUtil.getBundle(this.getClass());
            logger.debug("当前 Bundle: " + (currentBundle != null ? currentBundle.getSymbolicName() : "null"));

            BundleContext bundleContext = currentBundle != null ? currentBundle.getBundleContext() : null;
            logger.debug("BundleContext: " + (bundleContext != null ? "available" : "null"));

            if (bundleContext != null) {
                Bundle[] bundles = bundleContext.getBundles();
                logger.debug("找到 " + bundles.length + " 个 Bundle");

                for (Bundle bundle : bundles) {
                    logger.debug("Bundle: " + bundle.getSymbolicName() + " (状态: " + bundle.getState() + ")");

                    // 检查 Bundle 状态：RESOLVED(4), STARTING(8), ACTIVE(32) 都可以检查
                    if (bundle.getState() == Bundle.ACTIVE ||
                        bundle.getState() == Bundle.RESOLVED ||
                        bundle.getState() == Bundle.STARTING) {

                        logger.debug("检查 Bundle: " + bundle.getSymbolicName() + " (状态: " + bundle.getState() + ")");

                        // 检查 Bundle 是否包含目标包路径
                        if (bundleContainsPackage(bundle, packagePath)) {
                            logger.info("✅ 通过 Bundle " + bundle.getSymbolicName() + " 找到包 " + packagePath);
                            // 直接使用 Bundle 的类加载器
                            return bundle.adapt(org.osgi.framework.wiring.BundleWiring.class).getClassLoader();
                        }
                    } else {
                        logger.debug("跳过 Bundle: " + bundle.getSymbolicName() + " (状态: " + bundle.getState() + " 不在检查范围内)");
                    }
                }
                logger.warn("所有 Bundle 都不包含包 " + packagePath);
            }
        } catch (Exception e) {
            logger.warn("OSGi Bundle 类加载器查找失败: " + e.getMessage(), e);
        }

        // 回退到传统方式
        String[] knownClasses = {
            packagePath + ".IFeishuAuthenticatorEnhancer",
            packagePath + ".FeishuDefaultAuthenticatorEnhancer",
            packagePath + ".FeishuLicensedAuthenticatorEnhancer",
            packagePath + ".Activator"
        };

        for (String className : knownClasses) {
            try {
                Class<?> clazz = Class.forName(className);
                return clazz.getClassLoader();
            } catch (ClassNotFoundException e) {
                // 继续尝试下一个类
            }
        }

        return null;
    }

    /**
     * 检查 Bundle 是否包含指定的包路径
     */
    private boolean bundleContainsPackage(Bundle bundle, String packagePath) {
        try {
            BundleWiring bundleWiring = bundle.adapt(BundleWiring.class);
            if (bundleWiring != null) {
                // 检查 Bundle 是否导出了这个包
                List<org.osgi.framework.wiring.BundleCapability> capabilities =
                    bundleWiring.getCapabilities(org.osgi.framework.wiring.BundleRevision.PACKAGE_NAMESPACE);

                for (org.osgi.framework.wiring.BundleCapability capability : capabilities) {
                    String exportedPackage = (String) capability.getAttributes().get(org.osgi.framework.wiring.BundleRevision.PACKAGE_NAMESPACE);
                    if (packagePath.equals(exportedPackage)) {
                        logger.debug("Bundle " + bundle.getSymbolicName() + " 导出包: " + exportedPackage);
                        return true;
                    }
                }

                // 如果没有导出，尝试检查 Bundle 内部是否包含这个包的资源
                String packageDir = packagePath.replace('.', '/');
                java.net.URL packageResource = bundle.getResource(packageDir);
                if (packageResource != null) {
                    logger.debug("Bundle " + bundle.getSymbolicName() + " 包含包资源: " + packageDir);
                    return true;
                }
            }
        } catch (Exception e) {
            logger.debug("检查 Bundle " + bundle.getSymbolicName() + " 是否包含包 " + packagePath + " 时发生异常: " + e.getMessage());
        }

        return false;
    }

    /**
     * 扫描文件系统中的类
     */
    private void scanFileSystemClasses(String packageDir, String packagePath, List<Class<?>> classes) {
        try {
            java.io.File dir = new java.io.File(packageDir);
            if (!dir.exists() || !dir.isDirectory()) {
                return;
            }

            java.io.File[] files = dir.listFiles();
            if (files == null) {
                return;
            }

            for (java.io.File file : files) {
                if (file.isDirectory()) {
                    // 递归扫描子目录
                    String subPackage = packagePath + "." + file.getName();
                    scanFileSystemClasses(file.getAbsolutePath(), subPackage, classes);
                } else if (file.getName().endsWith(".class")) {
                    // 加载类
                    String className = packagePath + "." + file.getName().substring(0, file.getName().length() - 6);
                    try {
                        Class<?> clazz = Class.forName(className);
                        classes.add(clazz);
                    } catch (Exception e) {
                        logger.debug("无法加载类: " + className, e);
                    }
                }
            }

        } catch (Exception e) {
            logger.error("扫描文件系统类失败: " + packageDir, e);
        }
    }

    /**
     * 扫描 JAR 文件中的类
     */
    private void scanJarClasses(java.net.URL jarUrl, String packagePath, List<Class<?>> classes) {
        try {
            String jarPath = jarUrl.getPath();
            if (jarPath.startsWith("file:")) {
                jarPath = jarPath.substring(5);
            }

            int exclamationIndex = jarPath.indexOf("!");
            if (exclamationIndex != -1) {
                jarPath = jarPath.substring(0, exclamationIndex);
            }

            try (java.util.jar.JarFile jarFile = new java.util.jar.JarFile(jarPath)) {
                java.util.Enumeration<java.util.jar.JarEntry> entries = jarFile.entries();
                String packageDir = packagePath.replace('.', '/');

                while (entries.hasMoreElements()) {
                    java.util.jar.JarEntry entry = entries.nextElement();
                    String entryName = entry.getName();

                    if (entryName.startsWith(packageDir) && entryName.endsWith(".class")) {
                        String className = entryName.replace('/', '.').substring(0, entryName.length() - 6);
                        try {
                            Class<?> clazz = Class.forName(className);
                            classes.add(clazz);
                        } catch (Exception e) {
                            logger.debug("无法加载JAR中的类: " + className, e);
                        }
                    }
                }
            }

        } catch (Exception e) {
            logger.error("扫描JAR文件类失败: " + jarUrl, e);
        }
    }

    @Override
    public boolean hasLicenseImplementation(Class<?> serviceInterface) {
        return licenseImplementationMappings.containsKey(serviceInterface);
    }

    @Override
    public boolean hasFallbackImplementation(Class<?> serviceInterface) {
        return fallbackImplementationMappings.containsKey(serviceInterface);
    }

    @Override
    public Map<Class<?>, Class<?>> getLicenseImplementationMappings() {
        return new HashMap<>(licenseImplementationMappings);
    }

    @Override
    public Map<Class<?>, Class<?>> getFallbackImplementationMappings() {
        return new HashMap<>(fallbackImplementationMappings);
    }

    @Override
    public Map<String, Class<?>> getNamedLicenseImplementationMappings() {
        return new HashMap<>(namedLicenseImplementationMappings);
    }

    @Override
    public Map<String, Class<?>> getNamedFallbackImplementationMappings() {
        return new HashMap<>(namedFallbackImplementationMappings);
    }

    @Override
    public List<Class<?>> getRegisteredServiceInterfaces() {
        Set<Class<?>> interfaces = new HashSet<>();
        interfaces.addAll(licenseImplementationMappings.keySet());
        interfaces.addAll(fallbackImplementationMappings.keySet());
        return new ArrayList<>(interfaces);
    }

    @Override
    public RegistrationStats getRegistrationStats() {
        return new RegistrationStats(
            licenseImplementationMappings.size(),
            fallbackImplementationMappings.size(),
            namedLicenseImplementationMappings.size(),
            namedFallbackImplementationMappings.size()
        );
    }

    @Override
    public void clear() {
        licenseImplementationMappings.clear();
        fallbackImplementationMappings.clear();
        namedLicenseImplementationMappings.clear();
        namedFallbackImplementationMappings.clear();
        implementationPriorities.clear();
        logger.debug("已清空所有注册映射");
    }

    /**
     * 获取实现类的优先级
     *
     * @param implementationClass 实现类
     * @return 优先级值
     */
    public int getImplementationPriority(Class<?> implementationClass) {
        return implementationPriorities.getOrDefault(implementationClass, 100);
    }

    /**
     * 获取服务接口的所有实现类（按优先级排序）
     *
     * @param serviceInterface 服务接口
     * @return 实现类列表（按优先级排序）
     */
    public List<Class<?>> getImplementationsByPriority(Class<?> serviceInterface) {
        List<Class<?>> implementations = new ArrayList<>();

        // 添加许可证实现
        Class<?> licenseImpl = licenseImplementationMappings.get(serviceInterface);
        if (licenseImpl != null) {
            implementations.add(licenseImpl);
        }

        // 添加回退实现
        Class<?> fallbackImpl = fallbackImplementationMappings.get(serviceInterface);
        if (fallbackImpl != null) {
            implementations.add(fallbackImpl);
        }

        // 按优先级排序（数字越小优先级越高）
        implementations.sort((a, b) -> Integer.compare(
            getImplementationPriority(a),
            getImplementationPriority(b)
        ));

        return implementations;
    }

    /**
     * 扫描 OSGi Bundle 中的类
     */
    private void scanBundleClasses(java.net.URL bundleResource, String packagePath, List<Class<?>> classes) {
        try {
            logger.debug("扫描 Bundle 资源: " + bundleResource);

            // 通过 Bundle 的类加载器直接扫描包中的类
            // 由于 Bundle 资源不能直接列举，我们尝试加载已知的类
            String[] knownClassNames = {
                "IFeishuAuthenticatorEnhancer",
                "FeishuDefaultAuthenticatorEnhancer",
                "FeishuLicensedAuthenticatorEnhancer",
                "Activator"
            };

            ClassLoader bundleClassLoader = null;

            // 尝试从当前的 Bundle 上下文中找到对应的 Bundle
            try {
                Bundle currentBundle = FrameworkUtil.getBundle(this.getClass());
                if (currentBundle != null) {
                    BundleContext bundleContext = currentBundle.getBundleContext();
                    if (bundleContext != null) {
                        Bundle[] bundles = bundleContext.getBundles();
                        for (Bundle bundle : bundles) {
                            if (bundleContainsPackage(bundle, packagePath)) {
                                bundleClassLoader = bundle.adapt(BundleWiring.class).getClassLoader();
                                logger.debug("找到对应的 Bundle 类加载器: " + bundle.getSymbolicName());
                                break;
                            }
                        }
                    }
                }
            } catch (Exception e) {
                logger.debug("查找 Bundle 类加载器失败: " + e.getMessage());
            }

            if (bundleClassLoader != null) {
                for (String className : knownClassNames) {
                    try {
                        String fullClassName = packagePath + "." + className;
                        Class<?> clazz = bundleClassLoader.loadClass(fullClassName);
                        classes.add(clazz);
                        logger.debug("成功加载 Bundle 类: " + fullClassName);
                    } catch (ClassNotFoundException e) {
                        // 这个类不存在，继续尝试下一个
                        logger.debug("Bundle 类不存在: " + packagePath + "." + className);
                    }
                }
            } else {
                logger.warn("无法获取 Bundle 类加载器，跳过 Bundle 类扫描");
            }

        } catch (Exception e) {
            logger.error("扫描 Bundle 类时发生异常: " + bundleResource, e);
        }
    }
}
