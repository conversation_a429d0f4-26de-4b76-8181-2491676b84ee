package com.fasnote.alm.plugin.manage.facade;

import com.fasnote.alm.plugin.manage.bootstrap.LicenseFrameworkBootstrap;
import com.fasnote.alm.plugin.manage.security.SecurityValidator;

/**
 * 安全管理门面
 * 职责：安全相关操作
 * 
 * 功能：
 * - 机器码获取
 * - 安全验证
 * - RSA密钥管理（支持开发/生产环境区分）
 * - 委托给SecurityValidator
 */
public class SecurityFacade {
    
    private final SecurityValidator securityValidator;
    
    public SecurityFacade() {
        this.securityValidator = new SecurityValidator();
    }

    /**
     * 构造函数（依赖注入）
     */
    public SecurityFacade(SecurityValidator securityValidator) {
        this.securityValidator = securityValidator;
    }
    
    /**
     * 获取当前机器码
     *
     * @return 机器码
     */
    public String getMachineCode() {
        return securityValidator.getMachineCode();
    }
    
    /**
     * 获取安全验证器
     */
    public SecurityValidator getSecurityValidator() {
        return securityValidator;
    }
}
