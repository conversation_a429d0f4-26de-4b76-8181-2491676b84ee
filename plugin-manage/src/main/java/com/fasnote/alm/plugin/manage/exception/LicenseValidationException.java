package com.fasnote.alm.plugin.manage.exception;

/**
 * 许可证验证异常
 */
public class LicenseValidationException extends LicenseException {
    
    private static final long serialVersionUID = 1L;
    
    public LicenseValidationException(String message, String pluginId) {
        super(message, "LICENSE_VALIDATION_FAILED", pluginId);
    }
    
    public LicenseValidationException(String message, String pluginId, Throwable cause) {
        super(message, "LICENSE_VALIDATION_FAILED", cause);
        setParameters(pluginId);
    }
}