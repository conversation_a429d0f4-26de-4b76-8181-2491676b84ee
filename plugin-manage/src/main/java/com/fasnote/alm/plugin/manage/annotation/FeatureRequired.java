package com.fasnote.alm.plugin.manage.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 功能权限验证注解
 * 
 * 标记需要特定功能权限的方法。
 * 被此注解标记的方法在调用前会验证当前许可证是否包含指定的功能权限。
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface FeatureRequired {
    
    /**
     * 需要的功能名称
     * 必须与许可证中定义的功能名称完全匹配
     */
    String value();
    
    /**
     * 功能别名（可选）
     * 当主功能名称不存在时，会检查别名
     */
    String[] aliases() default {};
    
    /**
     * 是否启用验证
     * 默认为true，设置为false可以临时禁用验证
     */
    boolean enabled() default true;
    
    /**
     * 验证失败时的处理策略
     */
    LicenseRequired.FailureStrategy onFailure() default LicenseRequired.FailureStrategy.THROW_EXCEPTION;
    
    /**
     * 自定义错误消息
     * 如果为空，则使用默认的错误消息
     */
    String message() default "";
    
    /**
     * 是否需要同时进行基本许可证验证
     * 默认为true，会先进行基本的许可证验证（时间、机器码等），然后再验证功能权限
     */
    boolean requireBasicValidation() default true;
}
