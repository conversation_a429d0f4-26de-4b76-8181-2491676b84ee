package com.fasnote.alm.plugin.manage.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 许可证验证注解
 * 
 * 标记需要进行许可证验证的方法。
 * 被此注解标记的方法在调用前会自动进行许可证验证，包括：
 * - 时间限制验证
 * - 机器码绑定验证
 * - 数字签名验证
 * - 完整性检查
 * 
 * 验证失败时会抛出SecurityException或执行降级策略。
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface LicenseRequired {
    
    /**
     * 是否启用验证
     * 默认为true，设置为false可以临时禁用验证（用于调试）
     */
    boolean enabled() default true;
    
    /**
     * 验证失败时的处理策略
     */
    FailureStrategy onFailure() default FailureStrategy.THROW_EXCEPTION;
    
    /**
     * 自定义错误消息
     * 如果为空，则使用默认的错误消息
     */
    String message() default "";
    
    /**
     * 验证失败处理策略枚举
     */
    enum FailureStrategy {
        /**
         * 抛出异常（默认策略）
         */
        THROW_EXCEPTION,
        
        /**
         * 返回null（适用于返回值可为null的方法）
         */
        RETURN_NULL,
        
        /**
         * 执行降级服务（需要配合降级服务实现）
         */
        DEGRADED_SERVICE,
        
        /**
         * 记录日志但继续执行（仅用于非关键功能）
         */
        LOG_AND_CONTINUE
    }
}
