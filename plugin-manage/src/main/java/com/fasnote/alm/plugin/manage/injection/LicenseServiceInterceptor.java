package com.fasnote.alm.plugin.manage.injection;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.injection.api.IDependencyInjector;
import com.fasnote.alm.injection.api.ILicenseValidator;
import com.fasnote.alm.injection.api.IServiceValidationInterceptor;
import com.fasnote.alm.injection.api.InterceptContext;

/**
 * 许可证服务拦截器
 * 
 * 在服务获取时进行许可证验证，根据验证结果决定返回正常实现还是降级实现
 * 这是实现自动降级策略的核心组件
 */
public class LicenseServiceInterceptor implements IServiceValidationInterceptor {
    
    private static final Logger logger = LoggerFactory.getLogger(LicenseServiceInterceptor.class);
    private static final String LOG_PREFIX = "[LicenseServiceInterceptor] ";
    
    private final ILicenseValidator licenseValidator;
    private final IDependencyInjector dependencyInjector;
    
    public LicenseServiceInterceptor(ILicenseValidator licenseValidator, IDependencyInjector dependencyInjector) {
        this.licenseValidator = licenseValidator;
        this.dependencyInjector = dependencyInjector;
    }
    
    @Override
    public <T> T interceptServiceRequest(Class<T> serviceClass, String serviceName, T normalService, InterceptContext context) {
        String pluginId = context.getPluginId();
        
        logger.debug(LOG_PREFIX + "拦截服务请求: {} (插件: {}, 服务名: {})", 
                   serviceClass.getSimpleName(), pluginId, serviceName);
        
        try {
            // 验证许可证
            boolean isLicenseValid = licenseValidator.validatePluginLicense(pluginId);
            
            if (isLicenseValid) {
                logger.debug(LOG_PREFIX + "许可证验证通过，返回正常服务实现: {}", serviceClass.getSimpleName());
                return normalService;
            } else {
                logger.warn(LOG_PREFIX + "许可证验证失败，切换到降级实现: {} (插件: {})", 
                          serviceClass.getSimpleName(), pluginId);
                return getFallbackService(serviceClass, serviceName);
            }
            
        } catch (Exception e) {
            logger.error(LOG_PREFIX + "拦截服务请求时发生异常，切换到降级实现: " + serviceClass.getSimpleName(), e);
            return getFallbackService(serviceClass, serviceName);
        }
    }
    
    @Override
    public boolean shouldIntercept(Class serviceClass, String serviceName) {
        // 只拦截许可证相关的服务
        if (serviceClass == null) {
            return false;
        }
        
        String className = serviceClass.getSimpleName();
        String packageName = serviceClass.getPackage() != null ? serviceClass.getPackage().getName() : "";
        
        // 拦截条件：
        // 1. 类名包含License
        // 2. 包名包含license
        // 3. 服务名包含license
        boolean shouldIntercept = className.contains("License") || 
                                packageName.contains("license") ||
                                (serviceName != null && serviceName.contains("license"));
        
        if (shouldIntercept) {
            logger.debug(LOG_PREFIX + "需要拦截服务: {} (服务名: {})", className, serviceName);
        }
        
        return shouldIntercept;
    }
    
    @Override
    public int getPriority() {
        return 100; // 中等优先级
    }
    
    /**
     * 获取降级服务实现
     *
     * @param serviceClass 服务接口类
     * @param serviceName 原始服务名称
     * @return 降级服务实例
     */
    private <T> T getFallbackService(Class<T> serviceClass, String serviceName) {
        try {
            // 1. 优先查找显式注册的 fallback 实现
            @SuppressWarnings("unchecked") T fallbackService = (T) dependencyInjector.getService(serviceClass, "fallback");
            if (fallbackService != null) {
                logger.debug(LOG_PREFIX + "找到显式注册的 fallback 服务: {}", serviceClass.getSimpleName());
                return fallbackService;
            }

            // 2. 向后兼容：尝试通过命名约定查找降级服务
            // 约定：降级服务名称 = 原服务名称 + "-fallback"
            String fallbackServiceName = null;
            if (serviceName != null) {
                fallbackServiceName = serviceName + "-fallback";
            } else {
                // 如果没有服务名称，使用接口名称 + "Fallback"
                fallbackServiceName = serviceClass.getSimpleName() + "Fallback";
            }

            @SuppressWarnings("unchecked") T legacyFallbackService = (T) dependencyInjector.getService(serviceClass, fallbackServiceName);
            if (legacyFallbackService != null) {
                logger.debug(LOG_PREFIX + "找到约定命名的降级服务: {} -> {}",
                           serviceName != null ? serviceName : serviceClass.getSimpleName(),
                           fallbackServiceName);
                return legacyFallbackService;
            }

            // 如果没有找到降级服务，记录警告并返回null
            logger.warn(LOG_PREFIX + "未找到降级服务实现: {} (查找名称: fallback, {})",
                      serviceClass.getSimpleName(), fallbackServiceName);
            return null;

        } catch (Exception e) {
            logger.error(LOG_PREFIX + "获取降级服务时发生异常: " + serviceClass.getSimpleName(), e);
            return null;
        }
    }
    
    /**
     * 检查服务是否为降级实现
     * 
     * @param serviceInstance 服务实例
     * @return 是否为降级实现
     */
    public boolean isFallbackService(Object serviceInstance) {
        if (serviceInstance == null) {
            return false;
        }
        
        String className = serviceInstance.getClass().getSimpleName();
        return className.contains("Fallback");
    }
    
    /**
     * 获取许可证验证器
     * 
     * @return 许可证验证器
     */
    public ILicenseValidator getLicenseValidator() {
        return licenseValidator;
    }
    
    /**
     * 获取依赖注入器
     * 
     * @return 依赖注入器
     */
    public IDependencyInjector getDependencyInjector() {
        return dependencyInjector;
    }
    
    /**
     * 获取拦截器描述
     * 
     * @return 拦截器描述
     */
    public String getDescription() {
        return "许可证服务拦截器，负责在服务获取时进行许可证验证，" +
               "根据验证结果自动切换到正常实现或降级实现";
    }
    
    /**
     * 获取拦截器版本
     * 
     * @return 拦截器版本
     */
    public String getVersion() {
        return "1.0.0";
    }
    
    /**
     * 获取拦截统计信息
     * 
     * @return 拦截统计信息
     */
    public String getInterceptStatistics() {
        // 这里可以添加拦截统计逻辑
        return "许可证服务拦截器统计信息：优先级=" + getPriority();
    }
}
