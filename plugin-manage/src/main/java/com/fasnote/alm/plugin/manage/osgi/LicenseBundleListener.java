package com.fasnote.alm.plugin.manage.osgi;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.osgi.framework.Bundle;
import org.osgi.framework.BundleEvent;
import org.osgi.framework.BundleListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.fasnote.alm.plugin.manage.core.LicenseManager;
import com.fasnote.alm.plugin.manage.model.ValidationResult;

/**
 * 许可证Bundle监听器
 * 监听OSGi Bundle的生命周期事件，当业务Bundle启动时自动激活对应的许可证
 * 
 * 核心功能：
 * 1. 监听Bundle启动事件
 * 2. 检查Bundle是否需要许可证
 * 3. 自动激活对应的许可证
 * 4. 加载许可证中的实现类
 */
public class LicenseBundleListener implements BundleListener {
    
    private static final Logger logger = LoggerFactory.getLogger(LicenseBundleListener.class);

    // 许可证管理器
    private final LicenseManager licenseManager;
    
    // Bundle激活状态跟踪
    private final Map<String, Boolean> bundleActivationStatus = new ConcurrentHashMap<>();
    
    /**
     * 构造函数
     * 
     * @param licenseManager 许可证管理器
     */
    public LicenseBundleListener(LicenseManager licenseManager) {
        this.licenseManager = licenseManager;
    }
    
    @Override
    public void bundleChanged(BundleEvent event) {
        Bundle bundle = event.getBundle();
        String bundleSymbolicName = bundle.getSymbolicName();
        
        if (bundleSymbolicName == null) {
            return; // 跳过没有符号名的Bundle
        }
        
        switch (event.getType()) {
            case BundleEvent.STARTED:
                handleBundleStarted(bundle);
                break;
                
            case BundleEvent.STOPPED:
                handleBundleStopped(bundle);
                break;
                
            case BundleEvent.RESOLVED:
                handleBundleResolved(bundle);
                break;
                
            case BundleEvent.UNRESOLVED:
                handleBundleUnresolved(bundle);
                break;
                
            default:
                // 其他事件暂不处理
                break;
        }
    }
    
    /**
     * 处理Bundle启动事件
     * 
     * @param bundle 启动的Bundle
     */
    private void handleBundleStarted(Bundle bundle) {
        String bundleSymbolicName = bundle.getSymbolicName();
        logger.info("Bundle启动: {}", bundleSymbolicName);

        try {
            // 检查Bundle是否需要许可证
            if (isBundleRequiresLicense(bundle)) {
                logger.info("Bundle需要许可证: {}", bundleSymbolicName);

                // 尝试激活许可证
                ValidationResult result = activateLicenseForBundle(bundleSymbolicName);
                if (result.isValid()) {
                    bundleActivationStatus.put(bundleSymbolicName, true);
                    logger.info("Bundle许可证激活成功: {}", bundleSymbolicName);
                } else {
                    bundleActivationStatus.put(bundleSymbolicName, false);
                    logger.warn("Bundle许可证激活失败: {}, 原因: {}", bundleSymbolicName, result.getMessage());
                }
            } else {
                logger.debug("Bundle不需要许可证: {}", bundleSymbolicName);
            }

        } catch (Exception e) {
            logger.error("处理Bundle启动事件失败: {}", bundleSymbolicName, e);
        }
    }
    
    /**
     * 处理Bundle停止事件
     * 
     * @param bundle 停止的Bundle
     */
    private void handleBundleStopped(Bundle bundle) {
        String bundleSymbolicName = bundle.getSymbolicName();
        logger.info("Bundle停止: {}", bundleSymbolicName);
        
        try {
            // 清理Bundle的激活状态
            bundleActivationStatus.remove(bundleSymbolicName);
            
            // 可以在这里添加许可证清理逻辑
            // 例如：移除Bundle相关的服务注册等
            
        } catch (Exception e) {
            logger.error("处理Bundle停止事件失败: {}", bundleSymbolicName, e);
        }
    }
    
    /**
     * 处理Bundle解析事件
     * 
     * @param bundle 解析的Bundle
     */
    private void handleBundleResolved(Bundle bundle) {
        String bundleSymbolicName = bundle.getSymbolicName();
        logger.debug("Bundle解析: {}", bundleSymbolicName);
        
        // Bundle解析时可以进行一些预处理
        // 例如：检查许可证文件是否存在等
    }
    
    /**
     * 处理Bundle未解析事件
     * 
     * @param bundle 未解析的Bundle
     */
    private void handleBundleUnresolved(Bundle bundle) {
        String bundleSymbolicName = bundle.getSymbolicName();
        logger.debug("Bundle未解析: {}", bundleSymbolicName);
    }
    
    /**
     * 检查Bundle是否需要许可证
     * 使用LicenseManager的统一检查方法，直接传递Bundle对象避免循环依赖
     *
     * @param bundle Bundle对象
     * @return 是否需要许可证
     */
    private boolean isBundleRequiresLicense(Bundle bundle) {
        String bundleSymbolicName = bundle.getSymbolicName();
        return licenseManager.isPluginRequiresLicense(bundle, bundleSymbolicName);
    }
    
    /**
     * 为Bundle激活许可证
     * 
     * @param bundleSymbolicName Bundle符号名
     * @return 激活结果
     */
    private ValidationResult activateLicenseForBundle(String bundleSymbolicName) {
        try {
            // 检查是否已经有预加载的许可证
            if (licenseManager.hasPluginLicense(bundleSymbolicName)) {
                // 激活许可证（加载实现类）
                return licenseManager.activatePluginLicense(bundleSymbolicName);
            } else {
                // 尝试从文件系统加载许可证
                return licenseManager.loadAndActivateLicenseFromFile(bundleSymbolicName);
            }
            
        } catch (Exception e) {
            logger.error("激活Bundle许可证失败: {}", bundleSymbolicName, e);
            return ValidationResult.failure("激活许可证失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取Bundle激活状态
     * 
     * @param bundleSymbolicName Bundle符号名
     * @return 激活状态
     */
    public boolean isBundleActivated(String bundleSymbolicName) {
        return bundleActivationStatus.getOrDefault(bundleSymbolicName, false);
    }
    
    /**
     * 获取所有激活的Bundle
     * 
     * @return 激活的Bundle列表
     */
    public Map<String, Boolean> getActivatedBundles() {
        return new ConcurrentHashMap<>(bundleActivationStatus);
    }
    
    /**
     * 清理资源
     */
    public void cleanup() {
        bundleActivationStatus.clear();
        logger.info("许可证Bundle监听器资源清理完成");
    }
}
