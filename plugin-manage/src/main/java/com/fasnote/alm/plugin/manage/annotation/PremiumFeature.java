package com.fasnote.alm.plugin.manage.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 高级功能注解
 * 标记需要高级许可证的功能
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface PremiumFeature {
    
    /**
     * 功能名称
     */
    String name() default "";
    
    /**
     * 功能描述
     */
    String description() default "";
    
    /**
     * 所需许可证级别
     */
    String requiredLevel() default "PREMIUM";
}