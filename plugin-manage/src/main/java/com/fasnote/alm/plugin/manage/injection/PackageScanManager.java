package com.fasnote.alm.plugin.manage.injection;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import org.osgi.framework.BundleContext;
import org.osgi.framework.ServiceReference;
import org.osgi.util.tracker.ServiceTracker;
import org.osgi.util.tracker.ServiceTrackerCustomizer;

import com.fasnote.alm.plugin.manage.api.IPackageScanProvider;
import com.polarion.core.util.logging.Logger;

/**
 * 包扫描管理器
 * 
 * 通过 OSGi 服务跟踪机制收集所有插件注册的包扫描提供者，
 * 统一管理需要扫描的包路径。
 */
public class PackageScanManager {
    
    private static final Logger logger = Logger.getLogger(PackageScanManager.class);
    
    private final BundleContext bundleContext;
    private final Map<String, IPackageScanProvider> scanProviders = new ConcurrentHashMap<>();
    private ServiceTracker<IPackageScanProvider, IPackageScanProvider> serviceTracker;

    // 包扫描提供者变更回调
    private PackageScanProviderChangeListener changeListener;

    /**
     * 包扫描提供者变更监听器
     */
    public interface PackageScanProviderChangeListener {
        /**
         * 当有新的包扫描提供者注册时调用
         */
        void onProviderAdded(IPackageScanProvider provider);

        /**
         * 当包扫描提供者被移除时调用
         */
        void onProviderRemoved(IPackageScanProvider provider);
    }

    public PackageScanManager(BundleContext bundleContext) {
        this.bundleContext = bundleContext;
    }

    /**
     * 设置变更监听器
     */
    public void setChangeListener(PackageScanProviderChangeListener listener) {
        this.changeListener = listener;
    }
    
    /**
     * 启动包扫描管理器
     */
    public void start() {
        if (bundleContext == null) {
            logger.warn("BundleContext 为空，使用 ServiceLoader 兼容模式");
            loadProvidersViaServiceLoader();
            return;
        }
        
        logger.info("启动包扫描管理器（OSGi 模式）");
        
        // 创建服务跟踪器
        serviceTracker = new ServiceTracker<>(
            bundleContext,
            IPackageScanProvider.class,
            new PackageScanProviderTracker()
        );
        
        serviceTracker.open();
        logger.info("包扫描服务跟踪器已启动");
    }
    
    /**
     * 停止包扫描管理器
     */
    public void stop() {
        if (serviceTracker != null) {
            serviceTracker.close();
            serviceTracker = null;
            logger.info("包扫描服务跟踪器已停止");
        }
        
        scanProviders.clear();
    }
    
    /**
     * 获取所有扫描包路径
     *
     * @return 包路径数组
     */
    public String[] getAllScanPackages() {
        Set<String> allPackages = new HashSet<>();

        // 按优先级排序提供者
        List<IPackageScanProvider> sortedProviders = new ArrayList<>(scanProviders.values());
        sortedProviders.sort(Comparator.comparingInt(IPackageScanProvider::getPriority));

        for (IPackageScanProvider provider : sortedProviders) {
            String[] packages = provider.getScanPackages();
            if (packages != null) {
                Collections.addAll(allPackages, packages);
            }
        }

        String[] result = allPackages.toArray(new String[0]);
        logger.info("收集到的扫描包路径: " + Arrays.toString(result));
        return result;
    }
    
    /**
     * 获取已注册的提供者数量
     * 
     * @return 提供者数量
     */
    public int getProviderCount() {
        return scanProviders.size();
    }
    
    /**
     * 获取所有提供者信息
     * 
     * @return 提供者信息列表
     */
    public List<String> getProviderInfo() {
        List<String> info = new ArrayList<>();
        for (IPackageScanProvider provider : scanProviders.values()) {
            info.add(String.format("%s (%s) - %s", 
                   provider.getName(), 
                   provider.getPluginId(),
                   Arrays.toString(provider.getScanPackages())));
        }
        return info;
    }
    
    /**
     * ServiceLoader 兼容模式
     */
    private void loadProvidersViaServiceLoader() {
        try {
            ServiceLoader<IPackageScanProvider> serviceLoader = ServiceLoader.load(IPackageScanProvider.class);
            for (IPackageScanProvider provider : serviceLoader) {
                addProvider(provider);
            }
            logger.info("通过 ServiceLoader 加载了 " + scanProviders.size() + " 个包扫描提供者");
        } catch (Exception e) {
            logger.error("通过 ServiceLoader 加载包扫描提供者失败", e);
        }
    }
    
    /**
     * 添加提供者
     */
    private void addProvider(IPackageScanProvider provider) {
        String key = provider.getPluginId() + "#" + provider.getName();
        scanProviders.put(key, provider);
        logger.info("添加包扫描提供者: " + provider.getName() + " -> " +
                  Arrays.toString(provider.getScanPackages()));

        // 触发变更回调
        if (changeListener != null) {
            try {
                changeListener.onProviderAdded(provider);
            } catch (Exception e) {
                logger.error("执行包扫描提供者添加回调时发生异常", e);
            }
        }
    }
    
    /**
     * 移除提供者
     */
    private void removeProvider(IPackageScanProvider provider) {
        String key = provider.getPluginId() + "#" + provider.getName();
        scanProviders.remove(key);
        logger.info("移除包扫描提供者: " + provider.getName());

        // 触发变更回调
        if (changeListener != null) {
            try {
                changeListener.onProviderRemoved(provider);
            } catch (Exception e) {
                logger.error("执行包扫描提供者移除回调时发生异常", e);
            }
        }
    }
    
    /**
     * 包扫描提供者服务跟踪器
     */
    private class PackageScanProviderTracker implements ServiceTrackerCustomizer<IPackageScanProvider, IPackageScanProvider> {
        
        @Override
        public IPackageScanProvider addingService(ServiceReference<IPackageScanProvider> reference) {
            IPackageScanProvider provider = bundleContext.getService(reference);
            if (provider != null) {
                addProvider(provider);
            }
            return provider;
        }
        
        @Override
        public void modifiedService(ServiceReference<IPackageScanProvider> reference, IPackageScanProvider provider) {
            // 服务修改时重新添加
            if (provider != null) {
                removeProvider(provider);
                addProvider(provider);
            }
        }
        
        @Override
        public void removedService(ServiceReference<IPackageScanProvider> reference, IPackageScanProvider provider) {
            if (provider != null) {
                removeProvider(provider);
            }
            bundleContext.ungetService(reference);
        }
    }
}
